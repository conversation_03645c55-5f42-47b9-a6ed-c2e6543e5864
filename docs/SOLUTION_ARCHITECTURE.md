# TikTok任务平台 - 解决方案架构文档

> **文档版本**: v1.0.0  
> **创建日期**: 2024-08-01  
> **架构师**: Senior Solution Architect  
> **项目阶段**: 架构设计阶段

## 📋 目录

- [1. 架构概览](#1-架构概览)
- [2. 技术栈选择](#2-技术栈选择)
- [3. 系统架构设计](#3-系统架构设计)
- [4. 项目结构设计](#4-项目结构设计)
- [5. 数据库架构](#5-数据库架构)
- [6. 安全架构](#6-安全架构)
- [7. 支付系统架构](#7-支付系统架构)
- [8. 性能与扩展性](#8-性能与扩展性)
- [9. 部署架构](#9-部署架构)
- [10. 监控运维](#10-监控运维)

## 1. 架构概览

### 1.1 业务背景
TikTok任务平台是一个基于社交媒体任务的USDT赚钱平台，用户通过完成TikTok相关任务获得数字货币奖励，支持三级推荐分销和VIP等级系统。

### 1.2 架构目标
- **高可用性**: 99.9%系统可用性
- **高性能**: 支持10万+并发用户
- **安全性**: 金融级安全保障
- **可扩展性**: 支持业务快速增长
- **可维护性**: 模块化设计，易于维护

### 1.3 技术约束
- **前端**: 已完成22个页面，基于HTML5+CSS3+JavaScript
- **移动端**: 移动优先设计，完美适配各种设备
- **支付**: 必须支持USDT(TRC20/ERC20)网络
- **合规**: 满足各地区数字货币监管要求

## 2. 技术栈选择

### 2.1 整体技术栈

```
┌─────────────────────────────────────────────────────────────┐
│                        技术栈架构                           │
├─────────────────────────────────────────────────────────────┤
│ 前端层    │ HTML5 + CSS3 + JavaScript + Tailwind CSS      │
│ 网关层    │ Nginx + Kong API Gateway                       │
│ 应用层    │ Node.js + Express/Fastify + TypeScript         │
│ 服务层    │ 微服务架构 (User/Task/Payment/Referral)        │
│ 数据层    │ MySQL 8.0 + Redis 6.0 + MongoDB               │
│ 消息层    │ Redis Pub/Sub + Bull Queue                     │
│ 缓存层    │ Redis + Nginx Cache + CDN                      │
│ 监控层    │ Prometheus + Grafana + ELK Stack               │
│ 部署层    │ Docker + Kubernetes + CI/CD                    │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术选择理由

#### **后端框架: Node.js + TypeScript**
- ✅ 与前端JavaScript技术栈统一
- ✅ 高并发异步处理能力
- ✅ 丰富的区块链/加密货币库
- ✅ 快速开发和原型验证
- ✅ 活跃的开源生态

#### **数据库: MySQL + Redis + MongoDB**
- **MySQL**: 主数据库，ACID特性保证金融数据一致性
- **Redis**: 缓存和会话存储，高性能键值存储
- **MongoDB**: 日志和分析数据，灵活的文档存储

#### **API网关: Kong**
- ✅ 统一入口，负载均衡
- ✅ 限流、鉴权、监控
- ✅ 插件生态丰富
- ✅ 支持微服务架构

## 3. 系统架构设计

### 3.1 整体架构图

```
                    ┌─────────────────┐
                    │   用户终端      │
                    │  (Mobile/Web)   │
                    └─────────┬───────┘
                              │ HTTPS
                    ┌─────────▼───────┐
                    │   CDN + WAF     │
                    │  (静态资源分发)  │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │  负载均衡器     │
                    │   (Nginx)       │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   API网关       │
                    │   (Kong)        │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼────────┐  ┌─────────▼────────┐  ┌────────▼────────┐
│   用户服务     │  │   任务服务       │  │   支付服务      │
│ (User Service) │  │ (Task Service)   │  │(Payment Service)│
└───────┬────────┘  └─────────┬────────┘  └────────┬────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
                    ┌─────────▼───────┐
                    │   数据存储层    │
                    │ MySQL + Redis   │
                    └─────────────────┘
```

### 3.2 微服务架构

#### **服务拆分策略**
```javascript
const microservices = {
  'user-service': {
    responsibilities: ['用户注册登录', '个人信息管理', '权限控制'],
    database: 'users, user_profiles, user_sessions',
    apis: ['/auth', '/users', '/profiles']
  },
  'task-service': {
    responsibilities: ['任务管理', '任务分配', '任务审核'],
    database: 'tasks, task_orders, task_categories',
    apis: ['/tasks', '/orders', '/submissions']
  },
  'payment-service': {
    responsibilities: ['USDT充值提现', '交易记录', '钱包管理'],
    database: 'transactions, wallets, addresses',
    apis: ['/payments', '/withdrawals', '/wallets']
  },
  'referral-service': {
    responsibilities: ['推荐关系', '佣金计算', '团队管理'],
    database: 'referrals, commissions, teams',
    apis: ['/referrals', '/commissions', '/teams']
  },
  'notification-service': {
    responsibilities: ['消息推送', '邮件通知', '短信验证'],
    database: 'notifications, templates, logs',
    apis: ['/notifications', '/sms', '/emails']
  }
};
```

## 4. 项目结构设计

### 4.1 推荐目录结构

```
tiktok-platform/
├── 📱 frontend/                    # 前端应用 (已完成)
│   ├── assets/                     # 静态资源
│   ├── components/                 # 组件库
│   ├── pages/                      # 页面文件
│   ├── utils/                      # 工具函数
│   └── dist/                       # 构建输出
│
├── 🔧 backend/                     # 后端服务
│   ├── services/                   # 微服务
│   │   ├── user-service/
│   │   │   ├── src/
│   │   │   │   ├── controllers/    # 控制器
│   │   │   │   ├── services/       # 业务逻辑
│   │   │   │   ├── models/         # 数据模型
│   │   │   │   ├── routes/         # 路由定义
│   │   │   │   └── middleware/     # 中间件
│   │   │   ├── tests/              # 测试文件
│   │   │   ├── package.json
│   │   │   └── Dockerfile
│   │   │
│   │   ├── task-service/           # 任务服务
│   │   ├── payment-service/        # 支付服务
│   │   ├── referral-service/       # 推荐服务
│   │   └── notification-service/   # 通知服务
│   │
│   ├── shared/                     # 共享模块
│   │   ├── database/               # 数据库连接
│   │   ├── middleware/             # 通用中间件
│   │   ├── utils/                  # 工具函数
│   │   ├── types/                  # TypeScript类型
│   │   └── config/                 # 配置文件
│   │
│   └── gateway/                    # API网关
│       ├── kong.yml               # Kong配置
│       ├── plugins/               # 自定义插件
│       └── docker-compose.yml
│
├── 🗄️ database/                    # 数据库
│   ├── migrations/                 # 数据库迁移
│   ├── seeds/                      # 测试数据
│   ├── schemas/                    # 数据库设计
│   └── backup/                     # 备份脚本
│
├── 🚀 deployment/                  # 部署配置
│   ├── docker/                     # Docker配置
│   ├── kubernetes/                 # K8s配置
│   ├── terraform/                  # 基础设施代码
│   └── scripts/                    # 部署脚本
│
├── 📚 docs/                        # 项目文档
│   ├── architecture/               # 架构文档
│   ├── api/                        # API文档
│   ├── deployment/                 # 部署文档
│   └── development/                # 开发文档
│
└── 🧪 tests/                       # 测试
    ├── unit/                       # 单元测试
    ├── integration/                # 集成测试
    ├── e2e/                        # 端到端测试
    └── performance/                # 性能测试
```

### 4.2 代码组织原则

#### **领域驱动设计 (DDD)**
```javascript
// 服务内部结构示例 - user-service
src/
├── controllers/        # 控制器层 - 处理HTTP请求
├── services/          # 业务逻辑层 - 核心业务逻辑
├── repositories/      # 数据访问层 - 数据库操作
├── models/           # 领域模型 - 业务实体
├── dto/              # 数据传输对象
├── validators/       # 数据验证
└── types/            # TypeScript类型定义

## 5. 数据库架构

### 5.1 数据库设计策略

#### **主数据库: MySQL 8.0**
```sql
-- 核心业务表设计
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    payment_password_hash VARCHAR(255),
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    parent_id BIGINT,
    vip_level TINYINT DEFAULT 0,
    vip_expire_time DATETIME,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_referral_code (referral_code),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES users(id)
);

-- 用户钱包表
CREATE TABLE user_wallets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    available_balance DECIMAL(18,8) DEFAULT 0.00000000,
    frozen_balance DECIMAL(18,8) DEFAULT 0.00000000,
    total_earnings DECIMAL(18,8) DEFAULT 0.00000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 平台收款地址表
CREATE TABLE platform_addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    network VARCHAR(20) NOT NULL, -- TRC20, ERC20
    address VARCHAR(100) NOT NULL,
    private_key_encrypted TEXT, -- 加密存储私钥
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_network_address (network, address)
);

-- 充值记录表
CREATE TABLE deposit_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tx_hash VARCHAR(100) NOT NULL,
    network VARCHAR(20) NOT NULL,
    from_address VARCHAR(100),
    to_address VARCHAR(100),
    amount DECIMAL(18,8) NOT NULL,
    encoded_amount DECIMAL(18,8) NOT NULL, -- 包含用户ID编码的金额
    confirmations INT DEFAULT 0,
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_tx_hash (tx_hash),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    type ENUM('like', 'follow', 'comment', 'share') NOT NULL,
    reward DECIMAL(10,4) NOT NULL,
    required_vip_level TINYINT DEFAULT 0,
    daily_limit INT DEFAULT 0,
    total_limit INT DEFAULT 0,
    completed_count INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_vip_level (required_vip_level),
    INDEX idx_status (status)
);
```

#### **缓存数据库: Redis 6.0**
```javascript
// Redis数据结构设计
const redisSchema = {
  // 用户会话
  'session:${userId}': {
    type: 'hash',
    ttl: 86400, // 24小时
    fields: ['token', 'loginTime', 'lastActivity']
  },

  // 任务缓存
  'tasks:active': {
    type: 'sorted_set',
    ttl: 3600, // 1小时
    score: 'priority'
  },

  // 用户每日任务计数
  'user:${userId}:daily_tasks:${date}': {
    type: 'hash',
    ttl: 86400,
    fields: ['like_count', 'follow_count', 'comment_count']
  },

  // 支付处理队列
  'payment:queue': {
    type: 'list',
    description: '支付处理任务队列'
  }
};
```

### 5.2 数据库分片策略

#### **分库分表规则**
```javascript
const shardingStrategy = {
  users: {
    shardKey: 'user_id',
    shardCount: 16,
    rule: 'user_id % 16'
  },
  transactions: {
    shardKey: 'user_id + created_at',
    shardCount: 32,
    rule: 'hash(user_id) % 8 + month_partition'
  },
  task_orders: {
    shardKey: 'created_at',
    partitionType: 'monthly',
    retentionPeriod: '12 months'
  }
};
```

## 6. 安全架构

### 6.1 认证授权架构

#### **JWT双令牌机制**
```javascript
// 认证流程设计
const authFlow = {
  login: {
    input: 'username + password',
    process: 'bcrypt验证 → 生成JWT + RefreshToken',
    output: {
      accessToken: 'JWT(15分钟有效期)',
      refreshToken: 'UUID(7天有效期)',
      userInfo: '基础用户信息'
    }
  },

  apiAccess: {
    middleware: 'JWT验证中间件',
    process: '解析token → 验证签名 → 检查过期',
    fallback: 'token过期 → 返回401 → 前端自动刷新'
  },

  tokenRefresh: {
    input: 'refreshToken',
    process: '验证refreshToken → 生成新JWT',
    security: 'refreshToken一次性使用，用后即焚'
  }
};
```

#### **支付安全机制**
```javascript
// 支付密码验证
const paymentSecurity = {
  paymentPassword: {
    storage: 'bcrypt哈希存储',
    attempts: '5次错误锁定24小时',
    verification: '6位数字密码'
  },

  smsVerification: {
    provider: '阿里云短信服务',
    codeLength: 6,
    expiry: '5分钟',
    rateLimit: '1分钟1次，1小时5次'
  },

  transactionSigning: {
    algorithm: 'HMAC-SHA256',
    key: 'user_payment_key + timestamp',
    verification: '服务端重新计算签名对比'
  }
};
```

### 6.2 数据安全

#### **敏感数据加密**
```javascript
// 加密策略
const encryptionStrategy = {
  atRest: {
    algorithm: 'AES-256-GCM',
    keyManagement: 'AWS KMS / HashiCorp Vault',
    fields: ['phone', 'email', 'wallet_address', 'id_card']
  },

  inTransit: {
    protocol: 'TLS 1.3',
    certificate: 'Let\'s Encrypt / 商业SSL',
    hsts: 'Strict-Transport-Security header'
  },

  inMemory: {
    sensitiveData: '使用后立即清零',
    logging: '敏感字段脱敏记录',
    debugging: '生产环境禁用敏感信息输出'
  }
};
```

## 7. 支付系统架构

### 7.1 USDT集成架构

#### **多网络支持**
```javascript
// 区块链网络配置
const blockchainNetworks = {
  TRC20: {
    network: 'TRON',
    node: 'https://api.trongrid.io',
    contractAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
    confirmations: 19,
    fee: '2 USDT'
  },

  ERC20: {
    network: 'Ethereum',
    node: 'https://mainnet.infura.io/v3/YOUR_KEY',
    contractAddress: '******************************************',
    confirmations: 12,
    fee: '15 USDT'
  }
};
```

#### **简化钱包管理架构**
```javascript
// 统一收款地址管理
const walletArchitecture = {
  platformWallet: {
    purpose: '平台统一收款地址',
    networks: {
      TRC20: 'TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx', // 平台TRC20地址
      ERC20: '0xXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'  // 平台ERC20地址
    },
    security: '多重签名钱包，冷存储'
  },

  depositSystem: {
    method: '用户充值到统一地址',
    identification: '通过转账金额小数点识别用户',
    example: '用户ID 12345 充值100U → 转账 100.12345 USDT',
    monitoring: '监听平台地址的所有入账交易'
  },

  amountEncoding: {
    rule: '充值金额 + 0.用户ID后5位',
    examples: [
      'userID: 12345, 充值100U → 100.12345 USDT',
      'userID: 67890, 充值50U → 50.67890 USDT',
      'userID: 1, 充值200U → 200.00001 USDT'
    ],
    validation: '后端解析小数点后数字匹配用户ID'
  }
};
```

#### **充值金额编码系统**
```javascript
// 充值金额编码和解码逻辑
class DepositAmountEncoder {
  // 编码：将用户ID嵌入到充值金额中
  static encode(userId, amount) {
    // 获取用户ID的后5位数字
    const userIdSuffix = String(userId).padStart(5, '0').slice(-5);
    // 将用户ID作为小数点后的数字
    const encodedAmount = parseFloat(`${amount}.${userIdSuffix}`);
    return encodedAmount;
  }

  // 解码：从交易金额中提取用户ID
  static decode(encodedAmount) {
    const amountStr = encodedAmount.toFixed(8);
    const [integerPart, decimalPart] = amountStr.split('.');

    // 提取小数点后前5位作为用户ID
    const userIdStr = decimalPart.substring(0, 5);
    const userId = parseInt(userIdStr, 10);
    const actualAmount = parseFloat(integerPart);

    return { userId, actualAmount };
  }

  // 验证编码金额的有效性
  static validate(encodedAmount, expectedUserId) {
    const { userId, actualAmount } = this.decode(encodedAmount);
    return userId === expectedUserId;
  }
}

// 使用示例
const examples = [
  {
    userId: 12345,
    amount: 100,
    encoded: DepositAmountEncoder.encode(12345, 100), // 100.12345
    description: '用户12345充值100 USDT'
  },
  {
    userId: 1,
    amount: 50,
    encoded: DepositAmountEncoder.encode(1, 50), // 50.00001
    description: '用户1充值50 USDT'
  },
  {
    userId: 999999,
    amount: 200,
    encoded: DepositAmountEncoder.encode(999999, 200), // 200.99999
    description: '用户999999充值200 USDT（取后5位）'
  }
];
```

### 7.2 支付流程设计

#### **简化充值流程**
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant P as 支付服务
    participant B as 区块链监听服务
    participant D as 数据库

    U->>F: 点击充值，选择金额和网络
    F->>P: 请求充值信息(用户ID + 金额)
    P->>P: 计算编码金额(金额 + 0.用户ID后5位)
    P-->>F: 返回平台地址 + 编码金额 + 二维码
    F-->>U: 显示充值地址和精确金额

    Note over U: 用户向平台地址转账编码金额
    U->>B: 发起区块链转账(如: 100.12345 USDT)

    B->>B: 监听平台地址交易
    B->>P: 检测到新交易
    P->>P: 解析交易金额，提取用户ID
    P->>D: 验证用户ID，更新用户余额
    P-->>F: WebSocket推送到账通知
    F-->>U: 显示充值成功
```

#### **提现流程**
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant P as 支付服务
    participant R as 风控系统
    participant W as 钱包服务
    participant B as 区块链

    U->>F: 提交提现申请
    F->>P: 验证支付密码+短信
    P->>R: 风控检查
    R-->>P: 风控通过
    P->>W: 冻结用户余额
    W->>B: 发起区块链转账
    B-->>W: 转账确认
    W->>P: 扣除用户余额
    P-->>F: 提现成功通知

#### **区块链监听服务设计**
```javascript
// 区块链监听服务架构
class BlockchainMonitorService {
  constructor() {
    this.networks = {
      TRC20: {
        rpcUrl: 'https://api.trongrid.io',
        contractAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
        platformAddress: 'TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx'
      },
      ERC20: {
        rpcUrl: 'https://mainnet.infura.io/v3/YOUR_KEY',
        contractAddress: '******************************************',
        platformAddress: '0xXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'
      }
    };
  }

  // 监听平台地址的入账交易
  async monitorDeposits() {
    for (const [network, config] of Object.entries(this.networks)) {
      this.startNetworkMonitor(network, config);
    }
  }

  // 处理检测到的交易
  async processTransaction(network, txHash, amount, fromAddress) {
    try {
      // 1. 解码金额，提取用户ID
      const { userId, actualAmount } = DepositAmountEncoder.decode(amount);

      // 2. 验证用户ID是否存在
      const user = await UserService.findById(userId);
      if (!user) {
        console.log(`Invalid user ID in transaction: ${txHash}`);
        return;
      }

      // 3. 检查交易是否已处理
      const existingRecord = await DepositRecord.findByTxHash(txHash);
      if (existingRecord) {
        return; // 避免重复处理
      }

      // 4. 创建充值记录
      const depositRecord = await DepositRecord.create({
        userId,
        txHash,
        network,
        fromAddress,
        toAddress: this.networks[network].platformAddress,
        amount: actualAmount,
        encodedAmount: amount,
        status: 'pending'
      });

      // 5. 等待确认
      this.waitForConfirmations(network, txHash, depositRecord.id);

    } catch (error) {
      console.error('Error processing transaction:', error);
    }
  }

  // 等待交易确认
  async waitForConfirmations(network, txHash, recordId) {
    const requiredConfirmations = network === 'TRC20' ? 19 : 12;

    const checkConfirmations = async () => {
      const confirmations = await this.getTransactionConfirmations(network, txHash);

      if (confirmations >= requiredConfirmations) {
        // 确认完成，更新用户余额
        await this.confirmDeposit(recordId);
      } else {
        // 继续等待
        setTimeout(checkConfirmations, 30000); // 30秒后再检查
      }
    };

    checkConfirmations();
  }

  // 确认充值到账
  async confirmDeposit(recordId) {
    const record = await DepositRecord.findById(recordId);
    if (record.status !== 'pending') return;

    // 开启数据库事务
    const transaction = await db.beginTransaction();

    try {
      // 1. 更新充值记录状态
      await DepositRecord.update(recordId, {
        status: 'confirmed',
        confirmedAt: new Date()
      }, { transaction });

      // 2. 增加用户余额
      await UserWallet.increaseBalance(record.userId, record.amount, { transaction });

      // 3. 提交事务
      await transaction.commit();

      // 4. 发送通知
      await NotificationService.sendDepositNotification(record.userId, record.amount);

      console.log(`Deposit confirmed: User ${record.userId}, Amount ${record.amount} USDT`);

    } catch (error) {
      await transaction.rollback();
      console.error('Error confirming deposit:', error);
    }
  }
}
```

## 8. 性能与扩展性

### 8.1 缓存架构

#### **多层缓存策略**
```javascript
// 缓存层级设计
const cacheArchitecture = {
  L1_Browser: {
    type: '浏览器缓存',
    content: '静态资源(CSS/JS/图片)',
    ttl: '7天',
    strategy: 'Cache-Control + ETag'
  },

  L2_CDN: {
    type: 'CDN边缘缓存',
    content: '静态资源 + API响应',
    ttl: '1小时',
    provider: 'CloudFlare / 阿里云CDN'
  },

  L3_Nginx: {
    type: 'Nginx缓存',
    content: '频繁访问的API响应',
    ttl: '5分钟',
    strategy: 'proxy_cache'
  },

  L4_Redis: {
    type: 'Redis应用缓存',
    content: '用户会话、任务列表、排行榜',
    ttl: '动态TTL',
    strategy: 'LRU淘汰策略'
  },

  L5_MySQL: {
    type: 'MySQL查询缓存',
    content: '查询结果缓存',
    size: '2GB',
    strategy: 'query_cache'
  }
};
```

### 8.2 数据库优化

#### **读写分离架构**
```javascript
// 主从复制配置
const mysqlCluster = {
  master: {
    role: '写操作',
    specs: '8C16G SSD',
    connections: 1000,
    binlog: 'ROW格式'
  },

  slaves: [
    {
      role: '读操作',
      specs: '4C8G SSD',
      connections: 2000,
      lag: '<1秒'
    },
    {
      role: '分析查询',
      specs: '8C16G SSD',
      connections: 500,
      purpose: '报表和数据分析'
    }
  ],

  proxy: {
    tool: 'ProxySQL',
    rules: '写操作→主库，读操作→从库',
    failover: '自动故障转移'
  }
};
```

### 8.3 负载均衡

#### **服务负载均衡**
```yaml
# Nginx负载均衡配置
upstream backend_servers {
    least_conn;
    server backend1:3000 weight=3 max_fails=3 fail_timeout=30s;
    server backend2:3000 weight=3 max_fails=3 fail_timeout=30s;
    server backend3:3000 weight=2 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    location /api/ {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 5s;
        proxy_read_timeout 30s;
    }
}
```

## 9. 部署架构

### 9.1 容器化部署

#### **Docker容器设计**
```dockerfile
# Node.js服务Dockerfile示例
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=nodejs:nodejs . .
USER nodejs
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

#### **Kubernetes部署配置**
```yaml
# user-service部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: tiktok-platform/user-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
```

### 9.2 环境管理

#### **多环境配置**
```javascript
// 环境配置管理
const environments = {
  development: {
    database: {
      host: 'localhost',
      port: 3306,
      database: 'tiktok_dev'
    },
    redis: {
      host: 'localhost',
      port: 6379
    },
    blockchain: {
      network: 'testnet',
      confirmations: 1
    }
  },

  staging: {
    database: {
      host: 'staging-db.internal',
      port: 3306,
      database: 'tiktok_staging'
    },
    redis: {
      host: 'staging-redis.internal',
      port: 6379
    },
    blockchain: {
      network: 'testnet',
      confirmations: 6
    }
  },

  production: {
    database: {
      host: 'prod-db-cluster.internal',
      port: 3306,
      database: 'tiktok_prod'
    },
    redis: {
      host: 'prod-redis-cluster.internal',
      port: 6379
    },
    blockchain: {
      network: 'mainnet',
      confirmations: 12
    }
  }
};
```

## 10. 监控运维

### 10.1 监控体系

#### **监控指标设计**
```javascript
// 关键监控指标
const monitoringMetrics = {
  business: {
    userRegistration: '用户注册数/小时',
    taskCompletion: '任务完成率',
    paymentSuccess: '支付成功率',
    userRetention: '用户留存率'
  },

  technical: {
    responseTime: 'API响应时间 < 500ms',
    errorRate: '错误率 < 0.1%',
    throughput: 'QPS处理能力',
    availability: '服务可用性 > 99.9%'
  },

  infrastructure: {
    cpuUsage: 'CPU使用率 < 80%',
    memoryUsage: '内存使用率 < 85%',
    diskUsage: '磁盘使用率 < 90%',
    networkLatency: '网络延迟 < 100ms'
  }
};
```

#### **告警策略**
```yaml
# Prometheus告警规则
groups:
- name: tiktok-platform-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"

  - alert: DatabaseConnectionHigh
    expr: mysql_global_status_threads_connected > 800
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Database connection count is high"
```

### 10.2 日志管理

#### **ELK Stack配置**
```yaml
# Logstash配置示例
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "user-service" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }

    if [level] == "ERROR" {
      mutate {
        add_tag => ["error"]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "tiktok-platform-%{+YYYY.MM.dd}"
  }
}
```

---

## 📋 实施计划

### Phase 1: 基础架构搭建 (4-6周)
- [ ] 搭建开发环境和CI/CD流水线
- [ ] 实现用户服务和认证系统
- [ ] 搭建数据库和缓存系统
- [ ] 实现API网关和负载均衡

### Phase 2: 核心业务开发 (6-8周)
- [ ] 实现任务管理系统
- [ ] 开发支付服务和USDT集成
- [ ] 实现推荐分销系统
- [ ] 开发VIP等级系统

### Phase 3: 安全加固和优化 (4-6周)
- [ ] 实施安全策略和风控系统
- [ ] 性能优化和压力测试
- [ ] 监控告警系统部署
- [ ] 生产环境部署和上线

### Phase 4: 运营支持 (持续)
- [ ] 管理后台开发
- [ ] 数据分析和报表系统
- [ ] 运维自动化工具
- [ ] 持续优化和迭代

---

## 🎯 架构决策记录

### ADR-001: 选择Node.js作为后端技术栈
**决策**: 使用Node.js + TypeScript作为后端开发语言
**理由**:
- 与前端技术栈统一，降低团队学习成本
- 高并发异步处理能力适合任务平台场景
- 丰富的区块链和加密货币库支持
**权衡**: 相比Java在企业级特性上稍弱，但开发效率更高

### ADR-002: 采用微服务架构
**决策**: 将系统拆分为用户、任务、支付、推荐等微服务
**理由**:
- 便于团队并行开发和独立部署
- 不同服务可以独立扩展
- 故障隔离，提高系统稳定性
**权衡**: 增加了系统复杂度和运维成本

### ADR-003: 选择MySQL作为主数据库
**决策**: 使用MySQL 8.0作为主要数据存储
**理由**:
- ACID特性保证金融数据一致性
- 成熟的主从复制和分片方案
- 团队熟悉度高，运维工具丰富
**权衡**: 在处理复杂查询时不如PostgreSQL灵活

### ADR-004: 前端采用纯HTML架构
**决策**: 保持现有HTML+CSS+JavaScript架构，不迁移到现代前端框架
**理由**:
- 22个页面已完成，开发成本低
- 移动端优化完善，用户体验良好
- 快速上线，验证商业模式
- 团队熟悉，维护成本低
**权衡**: 前端安全风险增加，需要后端承担更多安全责任

---

## 🛡️ **前端HTML架构的后端安全加固策略**

### 11.1 后端安全责任加重

#### **安全责任转移**
```javascript
// 前端HTML架构下的安全责任分配
const securityResponsibilities = {
  frontend: {
    responsibilities: [
      '基础输入验证（用户体验）',
      '敏感数据加密存储',
      'HTTPS强制使用',
      'XSS防护（输出编码）'
    ],
    limitations: [
      '所有验证可被绕过',
      '业务逻辑完全暴露',
      'API端点完全可见',
      '客户端数据可被篡改'
    ]
  },

  backend: {
    criticalResponsibilities: [
      '所有业务逻辑验证',
      '完整的输入验证和过滤',
      '严格的权限控制',
      '金融级数据安全',
      '防刷和风控系统',
      '完整的审计日志'
    ],
    securityPrinciple: '永远不信任前端数据，所有逻辑后端重新验证'
  }
};
```

### 11.2 API安全加固设计

#### **多层API安全验证**
```javascript
// API安全中间件栈
const apiSecurityStack = [
  {
    layer: 'Rate Limiting',
    purpose: '防止API滥用和DDoS攻击',
    implementation: 'Redis + Sliding Window',
    rules: {
      login: '5次/分钟/IP',
      payment: '10次/小时/用户',
      general: '1000次/小时/IP'
    }
  },

  {
    layer: 'Authentication',
    purpose: '用户身份验证',
    implementation: 'JWT + RefreshToken双令牌',
    security: {
      accessToken: '15分钟有效期',
      refreshToken: '7天有效期，一次性使用',
      blacklist: 'Redis黑名单机制'
    }
  },

  {
    layer: 'Authorization',
    purpose: '权限控制',
    implementation: 'RBAC + 资源级权限',
    rules: {
      userLevel: '普通用户只能访问自己的数据',
      vipLevel: 'VIP用户享有额外API权限',
      adminLevel: '管理员权限严格控制'
    }
  },

  {
    layer: 'Input Validation',
    purpose: '输入验证和过滤',
    implementation: 'Joi + 自定义验证器',
    rules: {
      sqlInjection: '严格的SQL注入防护',
      xssProtection: 'HTML标签过滤和编码',
      dataType: '严格的数据类型验证',
      businessLogic: '业务规则验证'
    }
  },

  {
    layer: 'Business Logic Validation',
    purpose: '业务逻辑验证',
    implementation: '后端完整重新验证',
    examples: {
      taskSubmission: '验证任务是否可执行、用户是否有权限',
      payment: '验证余额、限额、风控规则',
      referral: '验证推荐关系、佣金计算'
    }
  }
];
```

#### **关键API安全实现**
```javascript
// 支付API安全示例
class PaymentAPIController {
  async withdraw(req, res) {
    try {
      // 1. 身份验证（中间件已处理）
      const userId = req.user.id;

      // 2. 输入验证
      const { error, value } = withdrawSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: 'Invalid input' });
      }

      // 3. 业务权限验证
      const user = await User.findById(userId);
      if (!user || user.status !== 'active') {
        return res.status(403).json({ error: 'Account not active' });
      }

      // 4. 支付密码验证
      const isValidPaymentPassword = await bcrypt.compare(
        value.paymentPassword,
        user.paymentPasswordHash
      );
      if (!isValidPaymentPassword) {
        await this.recordFailedAttempt(userId, 'payment_password');
        return res.status(401).json({ error: 'Invalid payment password' });
      }

      // 5. 风控检查
      const riskCheck = await RiskControlService.checkWithdrawal(userId, value.amount);
      if (!riskCheck.passed) {
        await AuditLog.create({
          userId,
          action: 'withdrawal_blocked',
          reason: riskCheck.reason,
          ip: req.ip
        });
        return res.status(403).json({ error: 'Risk control blocked' });
      }

      // 6. 余额验证
      const wallet = await UserWallet.findByUserId(userId);
      if (wallet.availableBalance < value.amount) {
        return res.status(400).json({ error: 'Insufficient balance' });
      }

      // 7. 执行业务逻辑
      const withdrawal = await WithdrawalService.process(userId, value);

      // 8. 审计日志
      await AuditLog.create({
        userId,
        action: 'withdrawal_initiated',
        amount: value.amount,
        address: value.address,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.json({ success: true, withdrawalId: withdrawal.id });

    } catch (error) {
      console.error('Withdrawal error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
```

### 11.3 数据安全加固

#### **敏感数据处理策略**
```javascript
// 敏感数据安全处理
const sensitiveDataSecurity = {
  encryption: {
    algorithm: 'AES-256-GCM',
    keyManagement: 'AWS KMS / HashiCorp Vault',
    fields: [
      'phone', 'email', 'idCard', 'walletAddress',
      'paymentPassword', 'withdrawalAddress'
    ],
    implementation: `
      // 加密存储
      const encryptedPhone = encrypt(phone, userEncryptionKey);

      // 查询时解密
      const decryptedPhone = decrypt(user.phoneEncrypted, userEncryptionKey);
    `
  },

  masking: {
    purpose: '日志和API响应中的数据脱敏',
    rules: {
      phone: '138****5678',
      email: 'u***@example.com',
      walletAddress: 'TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx',
      amount: '仅显示给数据所有者'
    }
  },

  accessControl: {
    principle: '最小权限原则',
    implementation: '用户只能访问自己的敏感数据',
    adminAccess: '管理员访问需要额外审批和日志记录'
  }
};
```

### 11.4 风控系统设计

#### **多维度风控策略**
```javascript
// 风控系统架构
class RiskControlSystem {
  // 用户行为风控
  async checkUserBehavior(userId, action, params) {
    const checks = [
      this.checkFrequencyLimit(userId, action),
      this.checkAmountLimit(userId, params.amount),
      this.checkDeviceFingerprint(userId, params.deviceInfo),
      this.checkIPReputation(params.ip),
      this.checkTimePattern(userId, action)
    ];

    const results = await Promise.all(checks);
    return this.evaluateRisk(results);
  }

  // 支付风控
  async checkPaymentRisk(userId, amount, address) {
    const riskFactors = {
      // 用户历史行为
      userHistory: await this.getUserRiskScore(userId),

      // 金额异常检测
      amountRisk: this.checkAmountAnomaly(userId, amount),

      // 地址风险检测
      addressRisk: await this.checkAddressReputation(address),

      // 时间模式分析
      timeRisk: this.checkTimePattern(userId, 'withdrawal'),

      // 设备指纹
      deviceRisk: this.checkDeviceConsistency(userId)
    };

    return this.calculateRiskScore(riskFactors);
  }

  // 实时监控
  async monitorSuspiciousActivity() {
    const patterns = [
      'rapid_successive_operations',
      'unusual_amount_patterns',
      'suspicious_ip_changes',
      'abnormal_time_access',
      'multiple_account_correlation'
    ];

    for (const pattern of patterns) {
      await this.detectPattern(pattern);
    }
  }
}
```

### 11.5 审计和监控加强

#### **完整审计日志系统**
```javascript
// 审计日志设计
const auditLogSystem = {
  categories: {
    authentication: '登录、登出、密码修改',
    financial: '充值、提现、余额变动',
    business: '任务操作、VIP升级、推荐关系',
    security: '风控触发、异常行为、权限变更',
    admin: '管理员操作、系统配置变更'
  },

  logStructure: {
    timestamp: 'ISO 8601格式时间戳',
    userId: '用户ID（如适用）',
    action: '操作类型',
    resource: '操作的资源',
    result: '操作结果（成功/失败）',
    ip: '客户端IP地址',
    userAgent: '用户代理字符串',
    sessionId: '会话ID',
    riskScore: '风控评分',
    metadata: '额外的上下文信息'
  },

  retention: {
    financial: '7年（合规要求）',
    security: '2年',
    general: '1年',
    compression: '6个月后压缩存储'
  }
};
```

---

## 📚 相关专项文档

本文档为总体架构设计，以下专项文档提供详细的技术实现方案：

### 核心专项文档
| 文档 | 说明 | 状态 |
|------|------|------|
| [钱包系统架构设计](./钱包系统架构设计.md) | 简化钱包管理、金额编码识别、区块链监听服务 | ✅ 完成 |
| [前端HTML架构安全设计](./前端HTML架构安全设计.md) | 前端安全风险评估、后端安全加固策略 | ✅ 完成 |
| [API接口设计文档](./API接口设计文档.md) | 完整的RESTful API设计、请求响应格式、错误码定义 | ✅ 完成 |
| [数据库设计文档](./数据库设计文档.md) | 完整的数据库表结构、索引优化、分区策略 | ✅ 完成 |

### 待创建文档
- **部署运维文档** - 生产环境部署和运维指南
- **测试策略文档** - 功能测试、安全测试、性能测试方案
- **开发规范文档** - 代码规范、Git工作流、Code Review标准

### 文档使用指南
1. **架构师/技术负责人**: 阅读本总体架构文档
2. **后端开发**: 重点关注钱包系统和安全设计文档
3. **前端开发**: 重点关注前端HTML架构安全设计文档
4. **运维工程师**: 关注部署架构和监控运维章节
5. **测试工程师**: 关注安全测试和性能测试相关内容

---

**文档状态**: ✅ 总体架构设计完成，专项文档已拆分
**关键更新**: 拆分钱包系统和前端安全设计为独立文档
**下次更新**: 根据开发进展补充API和数据库设计文档
```
```
