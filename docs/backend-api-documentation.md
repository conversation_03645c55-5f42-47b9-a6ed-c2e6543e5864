# TikTok任务平台 - 后端API文档

## 技术栈
- **框架**: ThinkPHP 6.0+
- **数据库**: MySQL 8.0+
- **认证**: JWT Token
- **支付**: USDT集成

## 项目结构
```
app/
├── controller/
│   ├── User.php          # 用户管理
│   ├── Task.php          # 任务系统
│   ├── Wallet.php        # 钱包系统
│   ├── Referral.php      # 推荐系统
│   ├── Vip.php           # VIP系统
│   └── Admin.php         # 管理后台
├── model/
│   ├── User.php
│   ├── Task.php
│   ├── Order.php
│   └── Transaction.php
└── middleware/
    └── Auth.php          # JWT认证中间件
```

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `referral_code` varchar(20) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `vip_level` tinyint(1) DEFAULT 0,
  `vip_expire_time` datetime DEFAULT NULL,
  `available_balance` decimal(10,2) DEFAULT 0.00,
  `frozen_balance` decimal(10,2) DEFAULT 0.00,
  `total_earnings` decimal(10,2) DEFAULT 0.00,
  `completed_tasks` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `referral_code` (`referral_code`)
);
```

### 任务表 (tasks)
```sql
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `type` varchar(50) NOT NULL,
  `reward` decimal(8,2) NOT NULL,
  `required_vip_level` tinyint(1) DEFAULT 0,
  `daily_limit` int(11) DEFAULT 0,
  `total_limit` int(11) DEFAULT 0,
  `completed_count` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 任务订单表 (task_orders)
```sql
CREATE TABLE `task_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `reward` decimal(8,2) NOT NULL,
  `status` tinyint(1) DEFAULT 0,
  `submit_proof` text,
  `reviewed_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 钱包交易记录表 (transactions)
```sql
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL,
  `balance_after` decimal(10,2) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `related_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## API接口文档

### 1. 用户认证接口

#### 用户注册
```
POST /api/user/register
Content-Type: application/json

{
  "username": "test123",
  "phone": "13800138000",
  "password": "123456",
  "referral_code": "TK2024ABC"
}

Response:
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 1,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

#### 用户登录
```
POST /api/user/login
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "123456"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": 1,
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user_info": {
      "username": "test123",
      "vip_level": 1,
      "available_balance": "128.56"
    }
  }
}
```

### 2. 任务系统接口

#### 获取任务列表
```
GET /api/task/list?page=1&limit=20
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": {
    "tasks": [
      {
        "id": 1,
        "title": "TikTok点赞任务",
        "reward": "2.50",
        "required_vip_level": 0,
        "completed_today": 5,
        "daily_limit": 15
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_count": 95
    }
  }
}
```

#### 接受任务
```
POST /api/task/accept
Authorization: Bearer {token}
Content-Type: application/json

{
  "task_id": 1
}

Response:
{
  "code": 200,
  "message": "任务接受成功",
  "data": {
    "order_id": 1001,
    "task_info": {
      "title": "TikTok点赞任务",
      "reward": "2.50",
      "description": "为指定视频点赞"
    }
  }
}
```

#### 提交任务
```
POST /api/task/submit
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_id": 1001,
  "proof": "截图链接或其他证明"
}

Response:
{
  "code": 200,
  "message": "任务提交成功，等待审核",
  "data": {
    "order_id": 1001,
    "status": "pending"
  }
}
```

### 3. 钱包系统接口

#### 获取钱包余额
```
GET /api/wallet/balance
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": {
    "total_balance": "128.56",
    "available_balance": "118.56",
    "frozen_balance": "10.00",
    "today_earnings": "15.75"
  }
}
```

#### 充值申请
```
POST /api/wallet/deposit
Authorization: Bearer {token}
Content-Type: application/json

{
  "amount": "100.00",
  "payment_method": "usdt"
}

Response:
{
  "code": 200,
  "message": "充值订单创建成功",
  "data": {
    "order_id": "D202401150001",
    "amount": "100.00",
    "usdt_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
    "expire_time": "2024-01-15 15:30:00"
  }
}
```

#### 提现申请
```
POST /api/wallet/withdraw
Authorization: Bearer {token}
Content-Type: application/json

{
  "amount": "50.00",
  "usdt_address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE"
}

Response:
{
  "code": 200,
  "message": "提现申请提交成功",
  "data": {
    "order_id": "W202401150001",
    "amount": "50.00",
    "fee": "2.00",
    "actual_amount": "48.00"
  }
}
```

### 4. 推荐系统接口

#### 获取推荐信息
```
GET /api/referral/info
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": {
    "referral_code": "TK2024ABC",
    "total_earnings": "1235.60",
    "today_earnings": "89.50",
    "direct_referrals": 24,
    "team_total": 156,
    "today_new": 8
  }
}
```

### 5. VIP系统接口

#### 获取VIP信息
```
GET /api/vip/info
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": {
    "current_level": 1,
    "expire_time": "2024-12-31 23:59:59",
    "experience": 750,
    "next_level_experience": 1000,
    "privileges": [
      {
        "name": "每日任务",
        "value": "15个/天"
      },
      {
        "name": "收益加成",
        "value": "+10%"
      }
    ]
  }
}
```

#### VIP升级
```
POST /api/vip/upgrade
Authorization: Bearer {token}
Content-Type: application/json

{
  "target_level": 2
}

Response:
{
  "code": 200,
  "message": "VIP升级成功",
  "data": {
    "new_level": 2,
    "cost": "30.00",
    "expire_time": "2025-01-15 23:59:59"
  }
}
```

## 管理后台接口

### 1. 管理员登录
```
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 2. 仪表板数据
```
GET /api/admin/dashboard
Authorization: Bearer {admin_token}

Response:
{
  "code": 200,
  "data": {
    "user_count": 1256,
    "task_count": 45,
    "today_revenue": "12560.50",
    "pending_withdrawals": 23
  }
}
```

### 3. 用户管理
```
GET /api/admin/users?page=1&limit=20
POST /api/admin/users/{id}/status    # 启用/禁用用户
PUT /api/admin/users/{id}            # 编辑用户信息
```

### 4. 任务管理
```
GET /api/admin/tasks
POST /api/admin/tasks                # 创建任务
PUT /api/admin/tasks/{id}           # 编辑任务
DELETE /api/admin/tasks/{id}        # 删除任务
```

### 5. 订单审核
```
GET /api/admin/orders?status=pending
POST /api/admin/orders/{id}/review   # 审核任务订单
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 部署说明

### 环境要求
- PHP 7.4+
- MySQL 8.0+
- Redis (缓存)
- Composer

### 部署步骤
```bash
# 1. 克隆项目
git clone [repository-url]

# 2. 安装依赖
composer install

# 3. 配置数据库
cp .example.env .env
# 编辑.env文件配置数据库连接

# 4. 数据库迁移
php think migrate:run

# 5. 设置权限
chmod -R 755 runtime/
chmod -R 755 public/

# 6. 启动服务
php think run
```

### 生产环境配置
- 配置Nginx/Apache虚拟主机
- 启用HTTPS
- 配置定时任务处理
- 设置日志轮转
- 配置监控告警

## 安全考虑

1. **数据加密**: 敏感信息使用AES加密存储
2. **SQL注入**: 使用参数化查询
3. **XSS防护**: 输出数据进行HTML转义
4. **CSRF防护**: 表单提交验证CSRF Token
5. **频率限制**: API接口实现频率限制
6. **日志记录**: 记录所有关键操作日志

## 联系信息
- 开发团队: TikTok任务平台开发组
- 技术支持: <EMAIL>
- 更新日期: 2024-01-15