# TikTok任务平台 - 钱包系统架构设计

> **文档版本**: v1.0.0  
> **创建日期**: 2024-08-01  
> **架构师**: Senior Solution Architect  
> **专项**: 钱包系统与支付架构

## 📋 目录

- [1. 钱包架构概览](#1-钱包架构概览)
- [2. 简化钱包管理方案](#2-简化钱包管理方案)
- [3. 充值金额编码系统](#3-充值金额编码系统)
- [4. 数据库设计](#4-数据库设计)
- [5. 支付流程设计](#5-支付流程设计)
- [6. 区块链监听服务](#6-区块链监听服务)
- [7. USDT网络集成](#7-usdt网络集成)

## 1. 钱包架构概览

### 1.1 设计理念

**核心思想**: 使用统一平台收款地址，通过充值金额编码识别用户，大幅简化钱包管理复杂度。

### 1.2 架构优势

```javascript
const advantages = {
  simplicity: '只需管理少数几个平台地址',
  costEffective: '大幅降低钱包管理成本',
  scalability: '支持无限用户扩展',
  security: '集中管理，安全性更高',
  maintenance: '运维复杂度显著降低'
};
```

### 1.3 技术对比

| 方案 | 传统方案 | 简化方案 |
|------|----------|----------|
| 地址管理 | 每用户一个地址 | 平台统一地址 |
| 私钥管理 | 数万个私钥 | 2-3个私钥 |
| 监听复杂度 | 监听所有用户地址 | 监听平台地址 |
| 用户识别 | 地址映射 | 金额编码 |
| 扩展性 | 线性增长复杂度 | 复杂度恒定 |

## 2. 简化钱包管理方案

### 2.1 统一收款地址架构

```javascript
// 平台钱包管理架构
const walletArchitecture = {
  platformWallet: {
    purpose: '平台统一收款地址',
    networks: {
      TRC20: {
        address: 'TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx',
        network: 'TRON',
        contractAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'
      },
      ERC20: {
        address: '0xXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
        network: 'Ethereum',
        contractAddress: '******************************************'
      }
    },
    security: '多重签名钱包，冷存储管理'
  },
  
  depositSystem: {
    method: '用户充值到统一地址',
    identification: '通过转账金额小数点识别用户',
    example: '用户ID 12345 充值100U → 转账 100.12345 USDT',
    monitoring: '监听平台地址的所有入账交易'
  }
};
```

### 2.2 用户识别机制

```javascript
// 金额编码识别系统
const identificationSystem = {
  rule: '充值金额 + 0.用户ID后5位',
  examples: [
    {
      userId: 12345,
      amount: 100,
      encodedAmount: 100.12345,
      description: '用户12345充值100 USDT'
    },
    {
      userId: 67890,
      amount: 50,
      encodedAmount: 50.67890,
      description: '用户67890充值50 USDT'
    },
    {
      userId: 1,
      amount: 200,
      encodedAmount: 200.00001,
      description: '用户1充值200 USDT'
    },
    {
      userId: 999999,
      amount: 150,
      encodedAmount: 150.99999,
      description: '用户999999充值150 USDT（取后5位）'
    }
  ],
  validation: '后端解析小数点后数字匹配用户ID'
};
```

## 3. 充值金额编码系统

### 3.1 编码算法实现

```javascript
// 充值金额编码和解码逻辑
class DepositAmountEncoder {
  /**
   * 编码：将用户ID嵌入到充值金额中
   * @param {number} userId - 用户ID
   * @param {number} amount - 充值金额
   * @returns {number} 编码后的金额
   */
  static encode(userId, amount) {
    // 获取用户ID的后5位数字，不足5位前面补0
    const userIdSuffix = String(userId).padStart(5, '0').slice(-5);
    
    // 将用户ID作为小数点后的数字
    const encodedAmount = parseFloat(`${amount}.${userIdSuffix}`);
    
    return encodedAmount;
  }
  
  /**
   * 解码：从交易金额中提取用户ID
   * @param {number} encodedAmount - 编码的金额
   * @returns {object} {userId, actualAmount}
   */
  static decode(encodedAmount) {
    const amountStr = encodedAmount.toFixed(8);
    const [integerPart, decimalPart] = amountStr.split('.');
    
    // 提取小数点后前5位作为用户ID
    const userIdStr = decimalPart.substring(0, 5);
    const userId = parseInt(userIdStr, 10);
    const actualAmount = parseFloat(integerPart);
    
    return { userId, actualAmount };
  }
  
  /**
   * 验证编码金额的有效性
   * @param {number} encodedAmount - 编码的金额
   * @param {number} expectedUserId - 期望的用户ID
   * @returns {boolean} 是否匹配
   */
  static validate(encodedAmount, expectedUserId) {
    const { userId } = this.decode(encodedAmount);
    return userId === expectedUserId;
  }
  
  /**
   * 批量生成编码金额示例
   * @param {Array} deposits - 充值记录数组
   * @returns {Array} 编码结果数组
   */
  static batchEncode(deposits) {
    return deposits.map(deposit => ({
      ...deposit,
      encodedAmount: this.encode(deposit.userId, deposit.amount)
    }));
  }
}

// 使用示例和测试
const testCases = [
  { userId: 1, amount: 10 },
  { userId: 12345, amount: 100 },
  { userId: 999999, amount: 500 },
  { userId: 42, amount: 25.5 }
];

console.log('编码测试结果:');
testCases.forEach(test => {
  const encoded = DepositAmountEncoder.encode(test.userId, test.amount);
  const decoded = DepositAmountEncoder.decode(encoded);
  const isValid = DepositAmountEncoder.validate(encoded, test.userId);
  
  console.log({
    original: test,
    encoded: encoded,
    decoded: decoded,
    valid: isValid
  });
});
```

### 3.2 边界情况处理

```javascript
// 特殊情况处理策略
const edgeCaseHandling = {
  largeUserId: {
    problem: '用户ID超过5位数',
    solution: '取用户ID的后5位数字',
    example: 'userId: 1234567 → 编码使用: 34567'
  },
  
  duplicateEncoding: {
    problem: '不同用户可能产生相同的后5位',
    solution: '数据库唯一约束 + 人工审核机制',
    mitigation: '充值前检查是否存在冲突'
  },
  
  smallAmount: {
    problem: '充值金额小于1 USDT',
    solution: '设置最小充值限额10 USDT',
    reason: '避免小数点精度问题'
  },
  
  precisionLoss: {
    problem: 'JavaScript浮点数精度问题',
    solution: '使用decimal.js库处理精确计算',
    implementation: 'const Decimal = require("decimal.js");'
  }
};
```

## 4. 数据库设计

### 4.1 核心表结构

```sql
-- 用户钱包表
CREATE TABLE user_wallets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    available_balance DECIMAL(18,8) DEFAULT 0.00000000,
    frozen_balance DECIMAL(18,8) DEFAULT 0.00000000,
    total_earnings DECIMAL(18,8) DEFAULT 0.00000000,
    total_deposits DECIMAL(18,8) DEFAULT 0.00000000,
    total_withdrawals DECIMAL(18,8) DEFAULT 0.00000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_balance (available_balance),
    INDEX idx_updated (updated_at)
);

-- 平台收款地址表
CREATE TABLE platform_addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    network VARCHAR(20) NOT NULL COMMENT 'TRC20, ERC20',
    address VARCHAR(100) NOT NULL COMMENT '收款地址',
    private_key_encrypted TEXT COMMENT '加密存储的私钥',
    contract_address VARCHAR(100) COMMENT 'USDT合约地址',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    daily_limit DECIMAL(18,8) DEFAULT 0 COMMENT '日限额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_network_address (network, address),
    INDEX idx_active (is_active)
);

-- 充值记录表
CREATE TABLE deposit_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tx_hash VARCHAR(100) NOT NULL COMMENT '交易哈希',
    network VARCHAR(20) NOT NULL COMMENT '网络类型',
    from_address VARCHAR(100) COMMENT '发送地址',
    to_address VARCHAR(100) COMMENT '接收地址',
    amount DECIMAL(18,8) NOT NULL COMMENT '实际充值金额',
    encoded_amount DECIMAL(18,8) NOT NULL COMMENT '编码金额',
    confirmations INT DEFAULT 0 COMMENT '确认数',
    required_confirmations INT DEFAULT 12 COMMENT '需要确认数',
    status ENUM('pending', 'confirmed', 'failed', 'expired') DEFAULT 'pending',
    block_number BIGINT COMMENT '区块高度',
    gas_fee DECIMAL(18,8) DEFAULT 0 COMMENT '手续费',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    expired_at TIMESTAMP NULL COMMENT '过期时间',
    
    UNIQUE KEY uk_tx_hash (tx_hash),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_network (network),
    INDEX idx_created (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 提现记录表
CREATE TABLE withdrawal_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    tx_hash VARCHAR(100) COMMENT '交易哈希',
    network VARCHAR(20) NOT NULL,
    to_address VARCHAR(100) NOT NULL COMMENT '提现地址',
    amount DECIMAL(18,8) NOT NULL COMMENT '提现金额',
    fee DECIMAL(18,8) NOT NULL COMMENT '手续费',
    actual_amount DECIMAL(18,8) NOT NULL COMMENT '实际到账金额',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    admin_id BIGINT COMMENT '审核管理员ID',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_network (network),
    INDEX idx_created (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 4.2 索引优化策略

```sql
-- 复合索引优化
CREATE INDEX idx_deposit_user_status_time ON deposit_records(user_id, status, created_at);
CREATE INDEX idx_withdrawal_user_status_time ON withdrawal_records(user_id, status, created_at);

-- 分区表策略（大数据量时）
ALTER TABLE deposit_records PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 5. 支付流程设计

### 5.1 简化充值流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant P as 支付服务
    participant B as 区块链监听服务
    participant D as 数据库

    U->>F: 点击充值，选择金额和网络
    F->>P: 请求充值信息(用户ID + 金额)
    P->>P: 计算编码金额(金额 + 0.用户ID后5位)
    P-->>F: 返回平台地址 + 编码金额 + 二维码
    F-->>U: 显示充值地址和精确金额

    Note over U: 用户向平台地址转账编码金额
    U->>B: 发起区块链转账(如: 100.12345 USDT)

    B->>B: 监听平台地址交易
    B->>P: 检测到新交易
    P->>P: 解析交易金额，提取用户ID
    P->>D: 验证用户ID，更新用户余额
    P-->>F: WebSocket推送到账通知
    F-->>U: 显示充值成功
```

### 5.2 提现流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant P as 支付服务
    participant R as 风控系统
    participant W as 钱包服务
    participant B as 区块链

    U->>F: 提交提现申请
    F->>P: 验证支付密码+短信
    P->>R: 风控检查
    R-->>P: 风控通过
    P->>W: 冻结用户余额
    W->>B: 发起区块链转账
    B-->>W: 转账确认
    W->>P: 扣除用户余额
    P-->>F: 提现成功通知
```

---

## 📋 实施计划

### Phase 1: 基础架构 (1-2周)
- [ ] 数据库表结构创建
- [ ] 平台地址生成和配置
- [ ] 基础API接口开发

### Phase 2: 监听服务 (2-3周)
- [ ] 区块链监听服务开发
- [ ] 交易处理逻辑实现
- [ ] 确认机制完善

### Phase 3: 安全加固 (1-2周)
- [ ] 风控系统集成
- [ ] 异常处理机制
- [ ] 监控告警系统

### Phase 4: 测试优化 (1周)
- [ ] 功能测试
- [ ] 压力测试
- [ ] 安全测试

---

**文档状态**: ✅ 钱包系统架构设计完成
**关键特性**: 简化管理、金额编码、统一监听
**下次更新**: 根据开发进展更新实现细节
