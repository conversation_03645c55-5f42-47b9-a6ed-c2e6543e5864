# TikTok任务平台 - 前端设计文档

## 1. 设计概览

### 1.1 设计理念
- **简洁现代**: 采用现代化的扁平设计风格
- **用户友好**: 注重用户体验和交互流畅性
- **移动优先**: 针对移动端优化的响应式设计
- **品牌一致**: 统一的色彩和视觉风格

### 1.2 技术栈
- **基础技术**: HTML5 + CSS3 + JavaScript (ES6+)
- **CSS框架**: Tailwind CSS
- **图标库**: Font Awesome 6.4.0
- **架构模式**: 单页应用 (SPA)

### 1.3 浏览器兼容性
- iOS Safari 12+
- Android Chrome 80+
- Desktop Chrome 90+
- Desktop Safari 14+
- Desktop Firefox 90+

## 2. 视觉设计规范

### 2.1 色彩系统

#### 主色调
```css
/* 主品牌色 - 蓝紫渐变 */
--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--primary-blue: #667eea;
--primary-purple: #764ba2;

/* 功能色 */
--success-green: #10b981;
--warning-yellow: #f59e0b; 
--error-red: #ef4444;
--info-blue: #3b82f6;
```

#### 中性色
```css
/* 文字颜色 */
--text-primary: #111827;    /* 主要文字 */
--text-secondary: #6b7280;  /* 次要文字 */
--text-tertiary: #9ca3af;   /* 辅助文字 */
--text-white: #ffffff;      /* 白色文字 */

/* 背景色 */
--bg-primary: #ffffff;      /* 主背景 */
--bg-secondary: #f9fafb;    /* 次背景 */
--bg-tertiary: #f3f4f6;     /* 三级背景 */
```

#### VIP等级色彩
```css
--vip-bronze: #cd7f32;      /* VIP1 青铜 */
--vip-silver: #c0c0c0;      /* VIP2 白银 */
--vip-gold: #ffd700;        /* VIP3 黄金 */
--vip-diamond: #b9f2ff;     /* VIP4 钻石 */
```

### 2.2 字体系统

#### 字体栈
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
             'Helvetica Neue', Arial, sans-serif;
```

#### 字体大小
```css
/* 标题字体 */
--font-size-h1: 2rem;       /* 32px */
--font-size-h2: 1.5rem;     /* 24px */
--font-size-h3: 1.25rem;    /* 20px */
--font-size-h4: 1.125rem;   /* 18px */

/* 正文字体 */
--font-size-base: 1rem;     /* 16px */
--font-size-sm: 0.875rem;   /* 14px */
--font-size-xs: 0.75rem;    /* 12px */
```

#### 字重
```css
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

### 2.3 间距系统
```css
/* Tailwind CSS 间距系统 */
--spacing-1: 0.25rem;   /* 4px */
--spacing-2: 0.5rem;    /* 8px */
--spacing-3: 0.75rem;   /* 12px */
--spacing-4: 1rem;      /* 16px */
--spacing-6: 1.5rem;    /* 24px */
--spacing-8: 2rem;      /* 32px */
```

### 2.4 圆角系统
```css
--radius-sm: 0.375rem;   /* 6px */
--radius-md: 0.5rem;     /* 8px */
--radius-lg: 0.75rem;    /* 12px */
--radius-xl: 1rem;       /* 16px */
--radius-2xl: 1.5rem;    /* 24px */
--radius-full: 9999px;   /* 圆形 */
```

### 2.5 阴影系统
```css
/* 卡片阴影 */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
```

## 3. 组件设计

### 3.1 按钮组件

#### 主要按钮
```css
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.2s;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
```

#### 次要按钮
```css
.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}
```

#### 危险按钮
```css
.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}
```

### 3.2 卡片组件
```css
.card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transition: all 0.2s;
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}
```

### 3.3 输入框组件
```css
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
```

### 3.4 导航栏组件
```css
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 8px 0;
  z-index: 50;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  color: #6b7280;
  transition: color 0.2s;
}

.nav-item.active {
  color: #667eea;
}
```

## 4. 页面布局

### 4.1 整体布局结构
```
┌─────────────────────────┐
│      Header/TopBar      │ <- 顶部导航（部分页面）
├─────────────────────────┤
│                         │
│      Main Content       │ <- 主要内容区域
│                         │
├─────────────────────────┤
│    Bottom Navigation    │ <- 底部导航栏
└─────────────────────────┘
```

### 4.2 容器规范
```css
/* 主容器 */
.container {
  max-width: 375px;
  margin: 0 auto;
  padding: 0 24px;
  min-height: 100vh;
  padding-bottom: 80px; /* 为底部导航留空间 */
}

/* 内容区域 */
.content {
  padding: 24px 0;
}
```

### 4.3 网格系统
```css
/* 2列网格 */
.grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* 3列网格 */
.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

/* 4列网格 */
.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
```

## 5. 页面详细设计

### 5.1 首页 (index.html)
#### 布局结构
- 顶部用户信息卡片
- 余额显示区域
- 快捷功能按钮组
- 任务列表区域
- 底部导航栏

#### 关键样式
```css
/* 用户信息卡片 */
.user-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* 任务卡片 */
.task-card {
  background: white;
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.task-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}
```

### 5.2 钱包页面 (wallet.html)
#### 布局结构
- 余额卡片（渐变背景）
- 今日收益展示
- 快捷操作按钮
- 收益统计图表
- 交易记录列表

#### 关键样式
```css
/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 1rem;
  padding: 1.5rem;
}

/* USDT图标 */
.usdt-icon {
  background: linear-gradient(135deg, #26A17B 0%, #22C55E 100%);
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 交易记录项 */
.transaction-item {
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
}

.transaction-item.income {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.transaction-item.expense {
  background: #fef2f2;
  border: 1px solid #fecaca;
}
```

### 5.3 VIP页面 (vip.html)
#### 布局结构
- VIP状态卡片
- 特权展示网格
- VIP等级对比表
- 专属任务预览
- 升级优惠活动

#### 关键样式
```css
/* VIP卡片 */
.vip-card {
  background: linear-gradient(135deg, #2d1b69 0%, #764ba2 50%, #667eea 100%);
  position: relative;
  overflow: hidden;
}

/* 闪光效果 */
.premium-shine {
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* VIP等级卡片 */
.vip-level-card {
  border: 2px solid transparent;
  transition: all 0.3s;
}

.vip-level-card.current {
  border-color: #667eea;
  background: #eff6ff;
}

.vip-level-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
```

### 5.4 推荐页面 (referral.html)
#### 布局结构
- 收益概览卡片
- 邀请码展示
- 分销层级说明
- 团队成员列表
- 推广素材下载

#### 关键样式
```css
/* 收益概览 */
.earnings-card {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

/* 邀请码卡片 */
.invitation-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 分销层级 */
.level-item {
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
}

.level-1 {
  background: linear-gradient(135deg, #fef2f2 0%, #fce7e7 100%);
  border: 1px solid #fecaca;
}

.level-2 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
}

.level-3 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
}
```

### 5.5 个人中心 (profile.html)
#### 布局结构
- 用户信息头部
- 统计数据展示
- 快捷功能网格
- 功能菜单列表
- 设置选项

#### 关键样式
```css
/* 用户头部 */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  position: relative;
}

/* 头像 */
.avatar {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f9fafb;
}
```

## 6. 响应式设计

### 6.1 断点设置
```css
/* Mobile First 方案 */
/* 默认: 375px (iPhone X标准) */

/* 小屏手机 */
@media (max-width: 360px) {
  .container {
    padding: 0 16px;
  }
}

/* 大屏手机 */
@media (min-width: 414px) {
  .container {
    max-width: 414px;
  }
}

/* 平板 */
@media (min-width: 768px) {
  .container {
    max-width: 480px;
  }
}
```

### 6.2 字体缩放
```css
/* 小屏设备 */
@media (max-width: 360px) {
  html {
    font-size: 14px;
  }
}

/* 大屏设备 */
@media (min-width: 414px) {
  html {
    font-size: 16px;
  }
}
```

## 7. 交互设计

### 7.1 触摸交互
```css
/* 按钮触摸反馈 */
.btn {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.btn:active {
  transform: scale(0.98);
}

/* 列表项触摸反馈 */
.list-item:active {
  background-color: #f3f4f6;
}
```

### 7.2 加载状态
```css
/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 7.3 过渡动画
```css
/* 页面切换动画 */
.page-transition {
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease-out;
}

.page-transition.active {
  opacity: 1;
  transform: translateX(0);
}

/* 卡片悬停效果 */
.card {
  transition: all 0.2s ease-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
```

## 8. 性能优化

### 8.1 CSS优化
- 使用Tailwind CSS减少CSS文件大小
- 利用CSS变量提高维护性
- 避免过度使用复杂选择器

### 8.2 图片优化
- 使用WebP格式图片
- 实施懒加载机制
- 提供多尺寸图片适配

### 8.3 动画优化
- 优先使用transform和opacity属性
- 避免在动画中使用layout属性
- 使用will-change提示浏览器优化

## 9. 可访问性

### 9.1 语义化HTML
```html
<!-- 使用语义化标签 -->
<main role="main">
  <section aria-label="用户余额信息">
    <h2>我的钱包</h2>
  </section>
</main>
```

### 9.2 ARIA属性
```html
<!-- 为交互元素添加ARIA标签 -->
<button aria-label="打开菜单" aria-expanded="false">
  <i class="fas fa-bars"></i>
</button>
```

### 9.3 颜色对比度
- 确保文字和背景的对比度至少为4.5:1
- 为色盲用户提供替代提示方式

## 10. 设计交付物

### 10.1 设计文件
- Figma设计稿
- 组件库文档
- 图标资源包
- 色彩规范文档

### 10.2 开发资源
- CSS样式库
- JavaScript组件
- 图片资源包
- 字体文件

### 10.3 文档资料
- 设计规范文档
- 组件使用指南
- 响应式适配说明
- 浏览器兼容性报告