# TikTok任务平台 - API接口设计文档

> **文档版本**: v1.0.0  
> **创建日期**: 2024-08-01  
> **架构师**: Senior Solution Architect  
> **专项**: 后端API接口设计与数据契约

## 📋 目录

- [1. API设计原则](#1-api设计原则)
- [2. 认证授权接口](#2-认证授权接口)
- [3. 用户管理接口](#3-用户管理接口)
- [4. 钱包支付接口](#4-钱包支付接口)
- [5. 任务系统接口](#5-任务系统接口)
- [6. 推荐系统接口](#6-推荐系统接口)
- [7. VIP系统接口](#7-vip系统接口)
- [8. 通用接口规范](#8-通用接口规范)

## 1. API设计原则

### 1.1 RESTful设计规范

```javascript
// API设计原则
const apiDesignPrinciples = {
  restful: {
    resources: '使用名词表示资源',
    methods: 'HTTP方法表示操作',
    stateless: '无状态设计',
    cacheable: '支持缓存机制'
  },
  
  naming: {
    urls: '小写字母，用连字符分隔',
    parameters: '驼峰命名法',
    responses: '统一的响应格式'
  },
  
  versioning: {
    strategy: 'URL路径版本控制',
    format: '/api/v1/resource',
    compatibility: '向后兼容原则'
  },
  
  security: {
    authentication: 'JWT Bearer Token',
    authorization: 'RBAC权限控制',
    validation: '严格的输入验证',
    rateLimit: '接口限流保护'
  }
};
```

### 1.2 统一响应格式

```javascript
// 成功响应格式
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功",
  "timestamp": "2024-08-01T10:30:00Z",
  "requestId": "req_1234567890"
}

// 错误响应格式
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数验证失败",
    "details": [
      {
        "field": "amount",
        "message": "金额必须大于0"
      }
    ]
  },
  "timestamp": "2024-08-01T10:30:00Z",
  "requestId": "req_1234567890"
}

// 分页响应格式
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## 2. 认证授权接口

### 2.1 用户认证

#### **POST /api/v1/auth/login**
用户登录接口

```javascript
// 请求参数
{
  "username": "user123",        // 用户名或手机号
  "password": "password123",    // 登录密码
  "captcha": "ABCD",           // 图形验证码
  "deviceInfo": {              // 设备信息
    "deviceId": "device_123",
    "platform": "web",
    "userAgent": "Mozilla/5.0..."
  }
}

// 响应数据
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "refresh_token_here",
    "expiresIn": 900,           // 15分钟
    "user": {
      "id": 12345,
      "username": "user123",
      "phone": "138****5678",
      "vipLevel": 1,
      "status": "active"
    }
  }
}
```

#### **POST /api/v1/auth/refresh**
刷新访问令牌

```javascript
// 请求参数
{
  "refreshToken": "refresh_token_here"
}

// 响应数据
{
  "success": true,
  "data": {
    "accessToken": "new_access_token",
    "refreshToken": "new_refresh_token",
    "expiresIn": 900
  }
}
```

#### **POST /api/v1/auth/logout**
用户登出

```javascript
// 请求头
Authorization: Bearer {accessToken}

// 响应数据
{
  "success": true,
  "message": "登出成功"
}
```

### 2.2 用户注册

#### **POST /api/v1/auth/register**
用户注册接口

```javascript
// 请求参数
{
  "phone": "13800138000",
  "password": "password123",
  "confirmPassword": "password123",
  "smsCode": "123456",
  "referralCode": "ABC123",     // 可选，推荐码
  "agreement": true             // 同意用户协议
}

// 响应数据
{
  "success": true,
  "data": {
    "userId": 12345,
    "message": "注册成功"
  }
}
```

#### **POST /api/v1/auth/send-sms**
发送短信验证码

```javascript
// 请求参数
{
  "phone": "13800138000",
  "type": "register",          // register, login, reset_password
  "captcha": "ABCD"
}

// 响应数据
{
  "success": true,
  "message": "验证码已发送",
  "data": {
    "expireTime": 300          // 5分钟有效期
  }
}
```

## 3. 用户管理接口

### 3.1 用户信息

#### **GET /api/v1/users/profile**
获取用户资料

```javascript
// 请求头
Authorization: Bearer {accessToken}

// 响应数据
{
  "success": true,
  "data": {
    "id": 12345,
    "username": "user123",
    "phone": "138****5678",
    "email": "u***@example.com",
    "avatar": "https://cdn.example.com/avatar.jpg",
    "realName": "张***",
    "vipLevel": 1,
    "vipExpireTime": "2024-12-31T23:59:59Z",
    "registeredAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-08-01T10:00:00Z",
    "status": "active"
  }
}
```

#### **PUT /api/v1/users/profile**
更新用户资料

```javascript
// 请求参数
{
  "avatar": "https://cdn.example.com/new_avatar.jpg",
  "email": "<EMAIL>",
  "realName": "张三"
}

// 响应数据
{
  "success": true,
  "message": "资料更新成功"
}
```

### 3.2 密码管理

#### **PUT /api/v1/users/password**
修改登录密码

```javascript
// 请求参数
{
  "oldPassword": "old_password",
  "newPassword": "new_password",
  "confirmPassword": "new_password"
}

// 响应数据
{
  "success": true,
  "message": "密码修改成功"
}
```

#### **PUT /api/v1/users/payment-password**
设置/修改支付密码

```javascript
// 请求参数
{
  "paymentPassword": "123456",     // 6位数字
  "smsCode": "123456",            // 短信验证码
  "oldPaymentPassword": "654321"  // 修改时需要
}

// 响应数据
{
  "success": true,
  "message": "支付密码设置成功"
}
```

## 4. 钱包支付接口

### 4.1 钱包信息

#### **GET /api/v1/wallet/balance**
获取钱包余额

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "availableBalance": "1234.56789012",
    "frozenBalance": "100.00000000",
    "totalEarnings": "5678.90123456",
    "totalDeposits": "3000.00000000",
    "totalWithdrawals": "1000.00000000",
    "lastUpdated": "2024-08-01T10:30:00Z"
  }
}
```

### 4.2 充值接口

#### **POST /api/v1/wallet/deposit/create**
创建充值订单

```javascript
// 请求参数
{
  "amount": "100.00",
  "network": "TRC20"           // TRC20 或 ERC20
}

// 响应数据
{
  "success": true,
  "data": {
    "depositId": "dep_1234567890",
    "address": "TXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx",
    "amount": "100.12345",     // 编码后的金额
    "network": "TRC20",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expireTime": "2024-08-01T11:00:00Z",
    "instructions": "请向地址转账精确金额 100.12345 USDT"
  }
}
```

#### **GET /api/v1/wallet/deposit/{depositId}/status**
查询充值状态

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "depositId": "dep_1234567890",
    "status": "confirmed",       // pending, confirmed, failed, expired
    "amount": "100.00000000",
    "network": "TRC20",
    "txHash": "0x1234567890abcdef...",
    "confirmations": 20,
    "requiredConfirmations": 19,
    "createdAt": "2024-08-01T10:00:00Z",
    "confirmedAt": "2024-08-01T10:05:00Z"
  }
}
```

### 4.3 提现接口

#### **POST /api/v1/wallet/withdrawal/create**
创建提现申请

```javascript
// 请求参数
{
  "amount": "50.00",
  "address": "TYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx",
  "network": "TRC20",
  "paymentPassword": "123456",
  "smsCode": "654321"
}

// 响应数据
{
  "success": true,
  "data": {
    "withdrawalId": "wd_1234567890",
    "amount": "50.00000000",
    "fee": "2.00000000",
    "actualAmount": "48.00000000",
    "address": "TYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx",
    "network": "TRC20",
    "status": "pending",
    "estimatedArrival": "2024-08-01T11:00:00Z"
  }
}
```

#### **GET /api/v1/wallet/withdrawal/{withdrawalId}/status**
查询提现状态

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "withdrawalId": "wd_1234567890",
    "status": "completed",       // pending, processing, completed, failed, cancelled
    "amount": "50.00000000",
    "fee": "2.00000000",
    "actualAmount": "48.00000000",
    "txHash": "0xabcdef1234567890...",
    "createdAt": "2024-08-01T10:00:00Z",
    "completedAt": "2024-08-01T10:30:00Z"
  }
}
```

### 4.4 交易记录

#### **GET /api/v1/wallet/transactions**
获取交易记录

```javascript
// 请求参数 (Query Parameters)
?type=all&page=1&pageSize=20&startDate=2024-07-01&endDate=2024-08-01

// 响应数据
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "tx_1234567890",
        "type": "deposit",         // deposit, withdrawal, task_reward, referral_commission
        "amount": "100.00000000",
        "status": "completed",
        "description": "USDT充值",
        "txHash": "0x1234567890...",
        "createdAt": "2024-08-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

## 5. 任务系统接口

### 5.1 任务列表

#### **GET /api/v1/tasks**
获取可用任务列表

```javascript
// 请求参数 (Query Parameters)
?type=all&vipLevel=1&page=1&pageSize=20

// 响应数据
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1001,
        "title": "TikTok点赞任务",
        "description": "为指定视频点赞",
        "type": "like",
        "reward": "2.50000000",
        "requiredVipLevel": 0,
        "dailyLimit": 10,
        "completedToday": 3,
        "totalLimit": 1000,
        "completedCount": 856,
        "estimatedTime": "2分钟",
        "instructions": "1. 点击链接进入视频\n2. 点击红心点赞\n3. 截图上传",
        "status": "active",
        "createdAt": "2024-08-01T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 15
    }
  }
}
```

#### **POST /api/v1/tasks/{taskId}/start**
开始执行任务

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "taskOrderId": "order_1234567890",
    "taskId": 1001,
    "status": "in_progress",
    "startTime": "2024-08-01T10:30:00Z",
    "expireTime": "2024-08-01T11:30:00Z",
    "instructions": "请在60分钟内完成任务并提交"
  }
}
```

#### **POST /api/v1/tasks/orders/{orderId}/submit**
提交任务完成证明

```javascript
// 请求参数 (multipart/form-data)
{
  "screenshot": File,          // 截图文件
  "description": "已完成点赞", // 完成说明
  "completedAt": "2024-08-01T10:45:00Z"
}

// 响应数据
{
  "success": true,
  "data": {
    "orderId": "order_1234567890",
    "status": "submitted",
    "message": "任务已提交，等待审核",
    "estimatedReviewTime": "10分钟"
  }
}
```

## 6. 推荐系统接口

### 6.1 推荐信息

#### **GET /api/v1/referral/info**
获取推荐信息

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "referralCode": "ABC123",
    "referralLink": "https://platform.com/register?code=ABC123",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "totalReferrals": 25,
    "activeReferrals": 18,
    "totalCommission": "1234.56789012",
    "todayCommission": "89.50000000",
    "commissionRates": {
      "level1": "30%",
      "level2": "15%",
      "level3": "8%"
    }
  }
}
```

#### **GET /api/v1/referral/commissions**
获取佣金记录

```javascript
// 请求参数 (Query Parameters)
?type=all&page=1&pageSize=20&startDate=2024-07-01&endDate=2024-08-01

// 响应数据
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "comm_1234567890",
        "fromUserId": 12346,
        "fromUsername": "user***",
        "type": "task_commission",    // task_commission, vip_commission
        "level": 1,
        "rate": "30%",
        "originalAmount": "10.00000000",
        "commissionAmount": "3.00000000",
        "description": "下级完成任务佣金",
        "createdAt": "2024-08-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 89
    }
  }
}
```

## 7. VIP系统接口

### 7.1 VIP信息

#### **GET /api/v1/vip/info**
获取VIP信息

```javascript
// 响应数据
{
  "success": true,
  "data": {
    "currentLevel": 1,
    "currentLevelName": "白银会员",
    "expireTime": "2024-12-31T23:59:59Z",
    "daysRemaining": 152,
    "privileges": [
      "每日任务额度提升至20个",
      "任务奖励加成10%",
      "推荐佣金加成5%",
      "专属客服支持"
    ],
    "nextLevel": {
      "level": 2,
      "name": "黄金会员",
      "price": "68.00000000",
      "privileges": [
        "每日任务额度提升至50个",
        "任务奖励加成20%",
        "推荐佣金加成10%",
        "优先任务分配"
      ]
    }
  }
}
```

#### **POST /api/v1/vip/upgrade**
VIP升级

```javascript
// 请求参数
{
  "targetLevel": 2,
  "paymentPassword": "123456"
}

// 响应数据
{
  "success": true,
  "data": {
    "orderId": "vip_1234567890",
    "fromLevel": 1,
    "toLevel": 2,
    "price": "68.00000000",
    "expireTime": "2025-08-01T10:30:00Z",
    "message": "VIP升级成功"
  }
}
```

---

## 📋 开发指南

### 接口开发优先级
1. **Phase 1**: 认证授权 + 用户管理
2. **Phase 2**: 钱包支付核心功能
3. **Phase 3**: 任务系统基础功能
4. **Phase 4**: 推荐系统 + VIP系统

### 测试要求
- **单元测试**: 覆盖率 > 80%
- **集成测试**: 核心业务流程测试
- **安全测试**: 输入验证、权限控制测试
- **性能测试**: 接口响应时间 < 500ms

---

**文档状态**: ✅ API接口设计完成
**覆盖范围**: 认证、用户、钱包、任务、推荐、VIP系统
**下次更新**: 根据开发进展补充具体实现细节
