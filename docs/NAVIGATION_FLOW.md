# TikTok任务平台 - 导航流程说明

## 📱 移动端适配说明

### 设计更新
- ✅ 移除了iOS状态栏模拟（9:41时间显示）
- ✅ 改为全屏移动端适配，支持所有移动设备
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 优化触摸体验和滚动性能

## 🔄 页面跳转流程

### 1. 主页 → 任务大厅 → 任务详情

```
index.html (主页)
    ↓ 点击"查看全部"或快捷操作
tasks.html (任务大厅)
    ↓ 点击任务卡片或"立即开始"
task-details.html (任务详情)
```

### 2. 具体跳转路径

#### 从主页跳转到任务详情
- **路径**: `index.html` → `task-details.html`
- **触发**: 点击推荐任务的"立即开始"按钮
- **数据传递**: 通过localStorage存储任务信息

#### 从任务大厅跳转到任务详情
- **路径**: `tasks.html` → `task-details.html`
- **触发**: 点击任务卡片或"立即开始"按钮
- **数据传递**: 通过localStorage存储任务信息

## 📱 用户操作流程

### 完整任务执行流程

1. **进入平台**
   - 用户访问 `index.html` (主页)
   - 查看今日任务进度和推荐任务

2. **选择任务**
   - 方式1: 主页点击"立即开始" → 直接进入任务详情
   - 方式2: 主页点击"查看全部" → 进入任务大厅 → 选择任务

3. **执行任务**
   - 进入 `task-details.html` (任务详情页)
   - 查看任务要求和奖励信息
   - 按步骤完成任务：
     - 步骤1: 点击进入TikTok ✅
     - 步骤2: 完成指定操作 (点赞/关注/分享/评论)
     - 步骤3: 上传截图证明

4. **提交审核**
   - 上传截图后启用提交按钮
   - 点击提交任务
   - 等待审核结果

## 🔧 技术实现

### 数据传递机制
```javascript
// 存储任务数据
const taskData = {
    type: 'like',           // 任务类型
    reward: '2.50',         // 奖励金额
    title: 'TikTok点赞任务', // 任务标题
    description: '为指定视频点赞', // 任务描述
    timestamp: Date.now()   // 时间戳
};
localStorage.setItem('currentTask', JSON.stringify(taskData));
```

### 页面跳转函数
```javascript
// 跳转到任务详情
function goToTaskDetails(taskType, reward, title, description) {
    // 存储任务信息
    localStorage.setItem('currentTask', JSON.stringify({
        type: taskType,
        reward: reward,
        title: title,
        description: description,
        timestamp: Date.now()
    }));
    
    // 页面跳转
    window.location.href = 'task-details.html';
}
```

### 动态内容更新
```javascript
// 任务详情页面加载时读取数据
document.addEventListener('DOMContentLoaded', function() {
    const taskData = localStorage.getItem('currentTask');
    if (taskData) {
        const task = JSON.parse(taskData);
        updateTaskDisplay(task);
    }
});
```

## 🎯 支持的任务类型

### 1. 点赞任务 (like)
- **操作**: 为指定TikTok视频点赞
- **奖励**: ¥2.50
- **要求**: 截图显示红心点赞状态

### 2. 关注任务 (follow)
- **操作**: 关注指定TikTok用户
- **奖励**: ¥5.00
- **要求**: 截图显示关注成功状态

### 3. 分享任务 (share)
- **操作**: 分享TikTok视频到社交平台
- **奖励**: ¥3.50
- **要求**: 截图显示分享成功

### 4. 评论任务 (comment)
- **操作**: 为TikTok视频发表正面评论
- **奖励**: ¥8.00
- **要求**: VIP 2+ 等级，截图显示评论内容

## 🔄 返回导航

### 返回路径
- **任务详情页**: 点击左上角返回箭头 → 返回上一页
- **任务大厅页**: 点击左上角返回箭头 → 返回主页
- **所有页面**: 支持浏览器后退按钮

### 导航按钮
```html
<!-- 统一的返回按钮样式 -->
<button onclick="history.back()" class="p-2 -ml-2 hover:bg-white/20 rounded-full transition-colors">
    <i class="fas fa-arrow-left text-xl"></i>
</button>
```

## 📋 测试检查清单

### 功能测试
- [ ] 主页推荐任务点击跳转
- [ ] 任务大厅任务卡片点击跳转
- [ ] 任务详情页面数据正确显示
- [ ] 不同任务类型内容动态更新
- [ ] 返回按钮正常工作
- [ ] localStorage数据传递正常

### 用户体验测试
- [ ] 页面跳转流畅无卡顿
- [ ] 任务信息显示准确
- [ ] 按钮点击反馈明显
- [ ] 移动端适配良好
- [ ] 动画效果自然

### 兼容性测试
- [ ] Chrome浏览器
- [ ] Safari浏览器
- [ ] Firefox浏览器
- [ ] 移动端浏览器
- [ ] 不同屏幕尺寸适配
- [ ] 横屏/竖屏切换

## 🚀 使用说明

1. **启动项目**
   ```bash
   # 使用Live Server或直接打开
   open index.html
   ```

2. **测试流程**
   - 打开主页 (`index.html`)
   - 点击推荐任务的"立即开始"按钮
   - 验证跳转到任务详情页面
   - 检查任务信息是否正确显示
   - 测试返回功能

3. **验证数据传递**
   - 打开浏览器开发者工具
   - 查看 Application → Local Storage
   - 确认 `currentTask` 数据存储正确

---

**更新时间**: 2024-07-30  
**版本**: v1.0  
**状态**: ✅ 集成完成