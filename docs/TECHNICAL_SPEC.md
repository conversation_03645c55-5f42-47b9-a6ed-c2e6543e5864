# TikTok任务平台 - 技术规格文档

## 📋 目录
- [项目架构](#项目架构)
- [技术栈](#技术栈)
- [数据库设计](#数据库设计)
- [API接口设计](#api接口设计)
- [安全策略](#安全策略)
- [部署方案](#部署方案)
- [性能指标](#性能指标)

## 🏗️ 项目架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API      │    │   数据存储      │
│                 │    │                 │    │                 │
│ • React/Vue     │◄──►│ • Node.js/Java  │◄──►│ • MySQL         │
│ • Tailwind CSS  │    │ • Express/Spring│    │ • Redis         │
│ • Mobile First  │    │ • JWT Auth      │    │ • MongoDB       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   第三方服务    │              │
         │              │                 │              │
         └──────────────►│ • USDT支付     │◄─────────────┘
                        │ • 短信验证      │
                        │ • 对象存储      │
                        │ • CDN加速       │
                        └─────────────────┘
```

### 微服务架构
```
├── 用户服务 (User Service)
│   ├── 注册登录
│   ├── 用户信息管理
│   └── 权限控制
├── 任务服务 (Task Service)
│   ├── 任务发布
│   ├── 任务分配
│   └── 任务审核
├── 支付服务 (Payment Service)
│   ├── USDT充值
│   ├── 提现处理
│   └── 交易记录
├── 分销服务 (Referral Service)
│   ├── 邀请关系
│   ├── 佣金计算
│   └── 收益分发
├── VIP服务 (VIP Service)
│   ├── 等级管理
│   ├── 特权分配
│   └── 订阅处理
└── 通知服务 (Notification Service)
    ├── 消息推送
    ├── 邮件通知
    └── 短信验证
```

## 💻 技术栈

### 前端技术栈
```javascript
{
  "framework": "React 18+ / Vue 3+",
  "ui": "Tailwind CSS + Headless UI",
  "state": "Redux Toolkit / Pinia",
  "routing": "React Router / Vue Router",
  "http": "Axios",
  "icons": "FontAwesome / Heroicons",
  "mobile": "PWA + Capacitor",
  "build": "Vite / Webpack"
}
```

### 后端技术栈
```javascript
{
  "runtime": "Node.js 18+ / Java 17+",
  "framework": "Express.js / Spring Boot",
  "database": "MySQL 8.0 + Redis 7.0",
  "orm": "Prisma / MyBatis-Plus",
  "auth": "JWT + Refresh Token",
  "validation": "Joi / Hibernate Validator",
  "docs": "Swagger/OpenAPI 3.0",
  "testing": "Jest / JUnit"
}
```

### DevOps技术栈
```yaml
infrastructure:
  cloud: "AWS / 阿里云"
  containers: "Docker + Kubernetes"
  ci_cd: "GitHub Actions / GitLab CI"
  monitoring: "Prometheus + Grafana"
  logging: "ELK Stack"
  
security:
  waf: "Cloudflare WAF"
  secrets: "AWS Secrets Manager"
  scanning: "Snyk / SonarQube"
```

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(500),
    vip_level TINYINT DEFAULT 1,
    vip_expire_time TIMESTAMP NULL,
    total_earnings DECIMAL(15,4) DEFAULT 0,
    available_balance DECIMAL(15,4) DEFAULT 0,
    frozen_balance DECIMAL(15,4) DEFAULT 0,
    invitation_code VARCHAR(20) UNIQUE,
    inviter_id BIGINT,
    status TINYINT DEFAULT 1, -- 1:正常 0:禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_invitation_code (invitation_code),
    INDEX idx_inviter_id (inviter_id),
    FOREIGN KEY (inviter_id) REFERENCES users(id)
);
```

#### 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    task_type ENUM('like', 'follow', 'comment', 'share') NOT NULL,
    tiktok_url VARCHAR(500) NOT NULL,
    reward_amount DECIMAL(10,4) NOT NULL,
    required_vip_level TINYINT DEFAULT 1,
    total_quantity INT NOT NULL,
    completed_quantity INT DEFAULT 0,
    status TINYINT DEFAULT 1, -- 1:进行中 2:已完成 0:已暂停
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_vip_level (required_vip_level)
);
```

#### 任务执行记录表 (task_executions)
```sql
CREATE TABLE task_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    task_id BIGINT NOT NULL,
    screenshot_urls JSON, -- 存储截图链接数组
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    reward_amount DECIMAL(10,4),
    reject_reason VARCHAR(500),
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewer_id BIGINT,
    
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    UNIQUE KEY uk_user_task (user_id, task_id)
);
```

#### 分销关系表 (referral_relationships)
```sql
CREATE TABLE referral_relationships (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    level1_referrer_id BIGINT, -- 一级推荐人
    level2_referrer_id BIGINT, -- 二级推荐人
    level3_referrer_id BIGINT, -- 三级推荐人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_level1 (level1_referrer_id),
    INDEX idx_level2 (level2_referrer_id),
    INDEX idx_level3 (level3_referrer_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (level1_referrer_id) REFERENCES users(id),
    FOREIGN KEY (level2_referrer_id) REFERENCES users(id),
    FOREIGN KEY (level3_referrer_id) REFERENCES users(id)
);
```

#### 佣金记录表 (commission_records)
```sql
CREATE TABLE commission_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    referrer_id BIGINT NOT NULL,
    referee_id BIGINT NOT NULL,
    task_execution_id BIGINT NOT NULL,
    commission_level TINYINT NOT NULL, -- 1,2,3级
    commission_rate DECIMAL(5,4) NOT NULL, -- 佣金比例
    commission_amount DECIMAL(10,4) NOT NULL,
    status ENUM('pending', 'paid') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at TIMESTAMP NULL,
    
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referee_id (referee_id),
    INDEX idx_status (status),
    FOREIGN KEY (referrer_id) REFERENCES users(id),
    FOREIGN KEY (referee_id) REFERENCES users(id),
    FOREIGN KEY (task_execution_id) REFERENCES task_executions(id)
);
```

#### 签到记录表 (checkin_records)
```sql
CREATE TABLE checkin_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    checkin_date DATE NOT NULL,
    consecutive_days INT NOT NULL,
    reward_amount DECIMAL(10,4) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_checkin_date (checkin_date),
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_user_date (user_id, checkin_date)
);
```

### Redis缓存设计
```javascript
// 缓存策略
const cacheKeys = {
  userSession: 'user:session:{userId}',
  userProfile: 'user:profile:{userId}',
  taskList: 'tasks:list:{vipLevel}',
  dailyTasks: 'user:daily_tasks:{userId}:{date}',
  referralStats: 'user:referral_stats:{userId}',
  checkinStatus: 'user:checkin:{userId}:{date}',
  vipPrivileges: 'vip:privileges:{level}'
};

// TTL设置
const cacheTTL = {
  userSession: 7 * 24 * 3600, // 7天
  userProfile: 30 * 60,       // 30分钟
  taskList: 10 * 60,          // 10分钟
  dailyTasks: 24 * 3600,      // 24小时
  referralStats: 60 * 60,     // 1小时
  checkinStatus: 24 * 3600    // 24小时
};
```

## 🔌 API接口设计

### RESTful API规范
```
Base URL: https://api.tiktok-task.com/v1
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

### 用户模块APIs
```javascript
// 用户注册
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "password123",
  "invitationCode": "TK2024ABC" // 可选
}

// 用户登录
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 获取用户信息
GET /users/profile
Response: {
  "id": 12345,
  "email": "<EMAIL>",
  "nickname": "张小明",
  "vipLevel": 1,
  "totalEarnings": "856.30",
  "availableBalance": "128.56",
  "invitationCode": "TK2024ABC"
}

// 更新用户信息
PUT /users/profile
{
  "nickname": "新昵称",
  "avatar": "头像文件"
}
```

### 任务模块APIs
```javascript
// 获取任务列表
GET /tasks?page=1&limit=20&vipLevel=1
Response: {
  "tasks": [
    {
      "id": 1001,
      "title": "TikTok点赞任务",
      "taskType": "like",
      "rewardAmount": "2.50",
      "requiredVipLevel": 1,
      "description": "为指定视频点赞"
    }
  ],
  "pagination": {
    "total": 156,
    "page": 1,
    "limit": 20
  }
}

// 获取任务详情
GET /tasks/{taskId}
Response: {
  "id": 1001,
  "title": "TikTok点赞任务",
  "description": "为指定视频点赞获得奖励",
  "tiktokUrl": "https://tiktok.com/@user/video/123",
  "rewardAmount": "2.50",
  "requiredVipLevel": 1,
  "steps": [
    "点击进入TikTok",
    "为视频点赞", 
    "上传截图证明"
  ]
}

// 提交任务
POST /tasks/{taskId}/submit
{
  "screenshots": ["screenshot1.jpg", "screenshot2.jpg"]
}
```

### 钱包模块APIs
```javascript
// 获取钱包信息
GET /wallet/balance
Response: {
  "totalBalance": "128.56",
  "availableBalance": "100.56",
  "frozenBalance": "28.00",
  "todayEarnings": "15.75"
}

// 充值USDT
POST /wallet/deposit
{
  "amount": "100.00",
  "paymentMethod": "usdt_trc20"
}

// 提现申请
POST /wallet/withdraw
{
  "amount": "50.00",
  "address": "TRC20钱包地址",
  "password": "支付密码"
}

// 交易记录
GET /wallet/transactions?page=1&limit=20&type=all
Response: {
  "transactions": [
    {
      "id": "tx_123456",
      "type": "task_reward",
      "amount": "+2.50",
      "status": "completed",
      "createdAt": "2024-07-28T10:30:00Z",
      "description": "TikTok点赞任务奖励"
    }
  ]
}
```

### VIP模块APIs
```javascript
// 获取VIP信息
GET /vip/info
Response: {
  "currentLevel": 1,
  "expireTime": "2024-12-31T23:59:59Z",
  "privileges": {
    "dailyTasks": 15,
    "rewardBonus": 0.1,
    "customerService": "priority"
  },
  "nextLevel": {
    "level": 2,
    "price": "30.00",
    "privileges": {
      "dailyTasks": 25,
      "rewardBonus": 0.2
    }
  }
}

// VIP升级
POST /vip/upgrade
{
  "targetLevel": 2,
  "paymentMethod": "balance"
}
```

### 分销模块APIs
```javascript
// 获取推荐统计
GET /referral/stats
Response: {
  "totalEarnings": "1235.60",
  "todayEarnings": "89.50",
  "directReferrals": 24,
  "totalTeam": 156,
  "todayNewReferrals": 8
}

// 获取团队列表
GET /referral/team?page=1&limit=20&level=1
Response: {
  "members": [
    {
      "id": 12346,
      "nickname": "李小美",
      "vipLevel": 2,
      "joinTime": "2024-07-25T08:00:00Z",
      "todayContribution": "45.60",
      "totalContribution": "520.80"
    }
  ]
}

// 获取佣金记录
GET /referral/commissions?page=1&limit=20
Response: {
  "commissions": [
    {
      "id": 1001,
      "refereeNickname": "李小美", 
      "commissionLevel": 1,
      "amount": "0.75",
      "createdAt": "2024-07-28T09:30:00Z",
      "status": "paid"
    }
  ]
}
```

## 🔐 安全策略

### 身份认证
```javascript
// JWT Token结构
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": 12345,
    "email": "<EMAIL>",
    "vipLevel": 1,
    "exp": 1640995200, // 过期时间
    "iat": 1640908800  // 签发时间
  }
}

// Refresh Token机制
const tokenConfig = {
  accessTokenExpiry: '15m',   // 访问令牌15分钟
  refreshTokenExpiry: '7d',   // 刷新令牌7天
  secretKey: process.env.JWT_SECRET,
  refreshSecretKey: process.env.JWT_REFRESH_SECRET
};
```

### 数据加密
```javascript
// 密码加密
const bcrypt = require('bcrypt');
const saltRounds = 12;

// 敏感信息加密
const crypto = require('crypto');
const algorithm = 'aes-256-gcm';
const key = process.env.ENCRYPTION_KEY;

// USDT地址加密存储
function encryptAddress(address) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key, iv);
  // 加密逻辑
}
```

### 风控策略
```javascript
// 请求频率限制
const rateLimit = {
  login: { attempts: 5, window: '15m' },
  register: { attempts: 3, window: '1h' },
  taskSubmit: { attempts: 50, window: '1d' },
  withdraw: { attempts: 5, window: '1d' }
};

// 反作弊检测
const antiCheat = {
  deviceFingerprinting: true,    // 设备指纹识别
  ipDetection: true,             // IP异常检测
  behaviorAnalysis: true,        // 行为分析
  machineDetection: true,        // 机器识别
  imageVerification: true        // 截图真实性验证
};

// 异常监控
const monitoring = {
  suspiciousActivity: {
    rapidTaskCompletion: true,   // 快速完成任务
    unusualLoginPattern: true,   // 异常登录模式
    massRegistration: true,      // 批量注册
    abnormalWithdrawal: true     // 异常提现
  },
  alertThresholds: {
    taskCompletionTime: 30,      // 任务完成最短时间(秒)
    dailyTaskLimit: 100,         // 每日任务上限
    withdrawalAmount: 1000       // 单次提现上限
  }
};
```

### 数据安全
```javascript
// 数据脱敏
const dataMasking = {
  email: (email) => email.replace(/(.{3}).*(@.*)/, '$1***$2'),
  phone: (phone) => phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
  usdt: (address) => address.replace(/(.{6}).*(.{6})/, '$1...$2')
};

// SQL注入防护
const sqlProtection = {
  parameterizedQueries: true,    // 参数化查询
  inputValidation: true,         // 输入验证
  escaping: true,                // 转义处理
  whitelistValidation: true      // 白名单验证
};

// XSS防护
const xssProtection = {
  inputSanitization: true,       // 输入净化
  outputEncoding: true,          // 输出编码
  csp: "default-src 'self'",     // 内容安全策略
  httpOnly: true                 // HttpOnly Cookie
};
```

## 🚀 部署方案

### 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    environment:
      - API_BASE_URL=https://api.tiktok-task.com
    
  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://user:pass@mysql:3306/tiktok_task
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=tiktok_task
    volumes:
      - mysql_data:/var/lib/mysql
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### Kubernetes部署
```yaml
# k8s-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tiktok-task-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tiktok-task-backend
  template:
    metadata:
      labels:
        app: tiktok-task-backend
    spec:
      containers:
      - name: backend
        image: tiktok-task/backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### CI/CD流程
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Tests
        run: |
          npm ci
          npm run test
          npm run build
          
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/
          kubectl rollout restart deployment/tiktok-task-backend
```

## 📊 性能指标

### 系统性能要求
```javascript
const performanceTargets = {
  response_time: {
    api_avg: '<200ms',           // API平均响应时间
    api_p95: '<500ms',           // 95分位响应时间
    page_load: '<2s',            // 页面加载时间
    task_submit: '<1s'           // 任务提交响应
  },
  
  throughput: {
    concurrent_users: 10000,     // 并发用户数
    requests_per_second: 5000,   // 每秒请求数
    tasks_per_minute: 1000,      // 每分钟任务处理数
    transactions_per_second: 100  // 每秒交易数
  },
  
  availability: {
    uptime: '99.9%',             // 系统可用性
    data_durability: '99.999%',  // 数据持久性
    recovery_time: '<30s',       // 故障恢复时间
    backup_frequency: '1h'       // 备份频率
  },
  
  scalability: {
    max_users: 1000000,          // 最大用户数
    max_daily_tasks: 100000,     // 日最大任务数
    storage_growth: '100GB/month', // 存储增长
    database_scaling: 'horizontal' // 数据库扩展方式
  }
};
```

### 监控指标
```javascript
const metrics = {
  business: {
    daily_active_users: 'DAU',
    task_completion_rate: '任务完成率',
    user_retention_rate: '用户留存率',
    average_revenue_per_user: 'ARPU',
    customer_acquisition_cost: 'CAC'
  },
  
  technical: {
    cpu_utilization: 'CPU使用率',
    memory_usage: '内存使用率',
    disk_io: '磁盘IO',
    network_throughput: '网络吞吐量',
    database_connections: '数据库连接数',
    cache_hit_rate: '缓存命中率',
    error_rate: '错误率',
    request_latency: '请求延迟'
  },
  
  security: {
    failed_login_attempts: '登录失败次数',
    suspicious_activities: '可疑活动',
    blocked_ips: '被封IP数',
    fraud_detection_alerts: '风控告警'
  }
};
```

### 容量规划
```javascript
const capacityPlanning = {
  year1: {
    users: 100000,
    daily_tasks: 10000,
    storage_gb: 500,
    bandwidth_mbps: 100,
    servers: 5
  },
  
  year2: {
    users: 500000,
    daily_tasks: 50000,
    storage_gb: 2000,
    bandwidth_mbps: 500,
    servers: 15
  },
  
  year3: {
    users: 1000000,
    daily_tasks: 100000,
    storage_gb: 5000,
    bandwidth_mbps: 1000,
    servers: 30
  }
};
```

## 🔧 开发规范

### 代码规范
```javascript
// ESLint配置
{
  "extends": ["airbnb", "prettier"],
  "rules": {
    "max-len": ["error", 100],
    "no-console": "warn",
    "prefer-const": "error"
  }
}

// Git提交规范
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 测试策略
```javascript
const testingStrategy = {
  unit_tests: {
    coverage: '80%+',
    framework: 'Jest',
    target: '业务逻辑函数'
  },
  
  integration_tests: {
    coverage: '60%+',
    framework: 'Supertest',
    target: 'API接口'
  },
  
  e2e_tests: {
    coverage: '核心流程',
    framework: 'Cypress',
    target: '用户关键路径'
  },
  
  performance_tests: {
    tool: 'Artillery.js',
    scenarios: '高并发场景',
    frequency: '每周执行'
  }
};
```

---

**文档版本**: v1.0  
**更新时间**: 2024-07-28  
**维护团队**: TikTok任务平台技术团队