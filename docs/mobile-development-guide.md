# TikTok任务平台 - 移动端开发指南

## 1. 移动端概述

### 1.1 移动端特点
- **触摸优先**: 所有交互都基于触摸操作设计
- **屏幕适配**: 支持多种屏幕尺寸和分辨率
- **性能敏感**: 移动设备性能相对有限，需要优化
- **网络环境**: 考虑弱网络环境下的用户体验

### 1.2 目标设备
- **主要设备**: iPhone 6+ (375px) / Android 主流机型
- **次要设备**: iPad (768px+) / 大屏手机 (414px+)
- **系统版本**: iOS 12+ / Android 8.0+

### 1.3 技术选型
- **基础技术**: 响应式Web应用 (PWA Ready)
- **UI框架**: Tailwind CSS + 自定义组件
- **JavaScript**: 原生ES6+ (无依赖框架)
- **架构**: 单页应用 (SPA) 模式

## 2. 移动端适配方案

### 2.1 视口配置
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta name="format-detection" content="telephone=no">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
```

### 2.2 响应式断点
```css
/* 移动端断点系统 */
:root {
  --mobile-small: 320px;   /* 小屏手机 */
  --mobile-medium: 375px;  /* 标准手机 */
  --mobile-large: 414px;   /* 大屏手机 */
  --tablet: 768px;         /* 平板 */
  --desktop: 1024px;       /* 桌面端 */
}

/* 媒体查询 */
@media (max-width: 360px) {
  /* 小屏适配 */
  .container { padding: 0 12px; }
  .text-base { font-size: 14px; }
}

@media (min-width: 375px) and (max-width: 413px) {
  /* 标准手机 */
  .container { max-width: 375px; }
}

@media (min-width: 414px) {
  /* 大屏手机 */
  .container { max-width: 414px; }
}
```

### 2.3 布局容器
```css
/* 主容器 */
.mobile-container {
  max-width: 375px;
  margin: 0 auto;
  min-height: 100vh;
  position: relative;
  background: #f9fafb;
}

/* 内容区域 */
.mobile-content {
  padding: 0 24px;
  padding-bottom: 88px; /* 底部导航高度 + 安全边距 */
}

/* 安全区域适配 */
.mobile-container {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}
```

## 3. 触摸交互优化

### 3.1 触摸目标尺寸
```css
/* 最小触摸目标 44px × 44px */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮触摸区域 */
.btn-mobile {
  padding: 12px 24px;
  min-height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
}

/* 列表项触摸区域 */
.list-item-mobile {
  padding: 16px 24px;
  min-height: 60px;
  display: flex;
  align-items: center;
}
```

### 3.2 触摸反馈
```css
/* 移除默认触摸高亮 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

/* 自定义触摸反馈 */
.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.touch-feedback:active::before {
  width: 300px;
  height: 300px;
}

/* 按钮按下效果 */
.btn-mobile:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}
```

### 3.3 滑动手势
```javascript
// 滑动手势处理
class SwipeHandler {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      threshold: 50,
      restraint: 100,
      allowedTime: 300,
      ...options
    };
    this.init();
  }

  init() {
    let startX, startY, startTime;
    
    this.element.addEventListener('touchstart', (e) => {
      const touch = e.touches[0];
      startX = touch.clientX;
      startY = touch.clientY;
      startTime = Date.now();
    }, { passive: true });

    this.element.addEventListener('touchend', (e) => {
      const touch = e.changedTouches[0];
      const distX = touch.clientX - startX;
      const distY = touch.clientY - startY;
      const elapsedTime = Date.now() - startTime;

      if (elapsedTime <= this.options.allowedTime) {
        if (Math.abs(distX) >= this.options.threshold && Math.abs(distY) <= this.options.restraint) {
          if (distX > 0) {
            this.options.onSwipeRight && this.options.onSwipeRight();
          } else {
            this.options.onSwipeLeft && this.options.onSwipeLeft();
          }
        }
      }
    }, { passive: true });
  }
}
```

## 4. 性能优化策略

### 4.1 资源加载优化
```html
<!-- 关键资源预加载 -->
<link rel="preload" href="https://cdn.tailwindcss.com" as="style">
<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">

<!-- 图片懒加载 -->
<img src="placeholder.jpg" data-src="actual-image.jpg" class="lazy-load" alt="描述">

<!-- DNS预解析 -->
<link rel="dns-prefetch" href="//api.example.com">
<link rel="dns-prefetch" href="//images.unsplash.com">
```

### 4.2 JavaScript性能优化
```javascript
// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 懒加载实现
const observerOptions = {
  root: null,
  rootMargin: '50px',
  threshold: 0.1
};

const imageObserver = new IntersectionObserver((entries, observer) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target;
      img.src = img.dataset.src;
      img.classList.remove('lazy-load');
      observer.unobserve(img);
    }
  });
}, observerOptions);

document.querySelectorAll('.lazy-load').forEach(img => {
  imageObserver.observe(img);
});
```

### 4.3 CSS性能优化
```css
/* 避免复杂选择器 */
/* 不推荐 */
.header .nav ul li a:hover { }

/* 推荐 */
.nav-link:hover { }

/* 使用transform和opacity进行动画 */
.smooth-animation {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 开启硬件加速 */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
}

/* 避免重排重绘 */
.optimize-animation {
  will-change: transform, opacity;
}
```

## 5. 用户体验优化

### 5.1 加载状态处理
```javascript
// 页面加载状态管理
class LoadingManager {
  constructor() {
    this.loadingCount = 0;
    this.loadingElement = document.getElementById('loading');
  }

  show() {
    this.loadingCount++;
    if (this.loadingElement) {
      this.loadingElement.classList.remove('hidden');
    }
  }

  hide() {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      if (this.loadingElement) {
        this.loadingElement.classList.add('hidden');
      }
    }
  }
}

// 使用示例
const loading = new LoadingManager();

async function fetchData() {
  loading.show();
  try {
    const response = await fetch('/api/data');
    const data = await response.json();
    return data;
  } finally {
    loading.hide();
  }
}
```

### 5.2 错误处理
```javascript
// 全局错误处理
class ErrorHandler {
  static show(message, type = 'error') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // 自动消失
    setTimeout(() => {
      toast.remove();
    }, 3000);
  }

  static handleNetworkError(error) {
    if (!navigator.onLine) {
      this.show('网络连接已断开，请检查网络设置', 'warning');
    } else {
      this.show('网络请求失败，请稍后重试', 'error');
    }
  }
}

// 网络状态监听
window.addEventListener('online', () => {
  ErrorHandler.show('网络连接已恢复', 'success');
});

window.addEventListener('offline', () => {
  ErrorHandler.show('网络连接已断开', 'warning');
});
```

### 5.3 数据缓存策略
```javascript
// 本地存储管理
class StorageManager {
  static set(key, value, expiry = null) {
    const data = {
      value: value,
      expiry: expiry ? Date.now() + expiry : null
    };
    localStorage.setItem(key, JSON.stringify(data));
  }

  static get(key) {
    const item = localStorage.getItem(key);
    if (!item) return null;

    const data = JSON.parse(item);
    if (data.expiry && Date.now() > data.expiry) {
      localStorage.removeItem(key);
      return null;
    }

    return data.value;
  }

  static remove(key) {
    localStorage.removeItem(key);
  }

  static clear() {
    localStorage.clear();
  }
}

// API请求缓存
class APICache {
  constructor() {
    this.cache = new Map();
    this.maxAge = 5 * 60 * 1000; // 5分钟
  }

  get(key) {
    const item = this.cache.get(key);
    if (item && Date.now() - item.timestamp < this.maxAge) {
      return item.data;
    }
    return null;
  }

  set(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  clear() {
    this.cache.clear();
  }
}
```

## 6. PWA支持

### 6.1 Service Worker
```javascript
// sw.js - Service Worker
const CACHE_NAME = 'tiktok-tasks-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/profile.html',
  '/wallet.html',
  '/vip.html',
  '/referral.html',
  'https://cdn.tailwindcss.com',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // 缓存优先，网络备用
        return response || fetch(event.request);
      })
  );
});
```

### 6.2 Web App Manifest
```json
{
  "name": "TikTok任务平台",
  "short_name": "TikTok Tasks",
  "description": "通过完成TikTok任务赚取USDT的平台",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#667eea",
  "orientation": "portrait",
  "icons": [
    {
      "src": "/icons/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### 6.3 PWA安装提示
```javascript
// PWA安装提示
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
  // 阻止默认安装提示
  e.preventDefault();
  deferredPrompt = e;
  
  // 显示自定义安装按钮
  const installButton = document.getElementById('install-button');
  if (installButton) {
    installButton.style.display = 'block';
    
    installButton.addEventListener('click', () => {
      deferredPrompt.prompt();
      deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('用户接受了安装');
        }
        deferredPrompt = null;
      });
    });
  }
});
```

## 7. 调试和测试

### 7.1 移动端调试
```javascript
// 移动端控制台
class MobileConsole {
  constructor() {
    this.logs = [];
    this.createConsole();
  }

  createConsole() {
    const consoleDiv = document.createElement('div');
    consoleDiv.id = 'mobile-console';
    consoleDiv.style.cssText = `
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: 200px;
      background: rgba(0,0,0,0.9);
      color: white;
      font-size: 12px;
      overflow-y: auto;
      z-index: 9999;
      display: none;
      padding: 10px;
    `;
    document.body.appendChild(consoleDiv);

    // 三指点击显示控制台
    let touchCount = 0;
    document.addEventListener('touchstart', (e) => {
      if (e.touches.length === 3) {
        touchCount++;
        if (touchCount >= 2) {
          this.toggle();
          touchCount = 0;
        }
      }
    });
  }

  log(message) {
    this.logs.push(message);
    const consoleDiv = document.getElementById('mobile-console');
    if (consoleDiv) {
      consoleDiv.innerHTML = this.logs.join('<br>');
      consoleDiv.scrollTop = consoleDiv.scrollHeight;
    }
  }

  toggle() {
    const consoleDiv = document.getElementById('mobile-console');
    if (consoleDiv) {
      consoleDiv.style.display = consoleDiv.style.display === 'none' ? 'block' : 'none';
    }
  }
}

// 在开发环境中启用
if (process.env.NODE_ENV === 'development') {
  const mobileConsole = new MobileConsole();
  window.mobileLog = (msg) => mobileConsole.log(msg);
}
```

### 7.2 性能监控
```javascript
// 性能监控
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.init();
  }

  init() {
    // 页面加载时间
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
      
      console.log('Performance Metrics:', this.metrics);
    });

    // FPS监控
    this.measureFPS();
  }

  measureFPS() {
    let lastTime = performance.now();
    let frames = 0;

    const measureFrame = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        this.metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
        frames = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFrame);
    };

    requestAnimationFrame(measureFrame);
  }

  reportMetrics() {
    // 发送性能数据到分析服务
    fetch('/api/performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(this.metrics)
    });
  }
}
```

## 8. 兼容性处理

### 8.1 iOS Safari兼容
```css
/* iOS Safari 滚动优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* iOS Safari 100vh问题 */
.full-height {
  height: 100vh;
  height: -webkit-fill-available;
}

/* iOS Safari 输入框缩放问题 */
input, textarea, select {
  font-size: 16px; /* 防止自动缩放 */
}
```

### 8.2 Android WebView兼容
```javascript
// Android WebView检测
function isAndroidWebView() {
  const ua = navigator.userAgent;
  return /Android/.test(ua) && /wv\)/.test(ua);
}

// Android WebView特殊处理
if (isAndroidWebView()) {
  // 禁用缩放
  document.addEventListener('gesturestart', (e) => {
    e.preventDefault();
  });
  
  // 处理键盘遮挡
  const originalHeight = window.innerHeight;
  window.addEventListener('resize', () => {
    if (window.innerHeight < originalHeight * 0.8) {
      document.body.classList.add('keyboard-open');
    } else {
      document.body.classList.remove('keyboard-open');
    }
  });
}
```

### 8.3 低版本浏览器支持
```javascript
// Polyfills
if (!Array.prototype.includes) {
  Array.prototype.includes = function(searchElement) {
    return this.indexOf(searchElement) !== -1;
  };
}

if (!String.prototype.startsWith) {
  String.prototype.startsWith = function(searchString, position) {
    position = position || 0;
    return this.substr(position, searchString.length) === searchString;
  };
}

// 检测并加载polyfills
function loadPolyfills() {
  const needsPolyfills = !window.Promise || !window.fetch;
  
  if (needsPolyfills) {
    const script = document.createElement('script');
    script.src = 'https://polyfill.io/v3/polyfill.min.js?features=Promise,fetch';
    document.head.appendChild(script);
  }
}

loadPolyfills();
```

## 9. 部署和发布

### 9.1 构建优化
```bash
# 压缩HTML
npm install -g html-minifier
html-minifier --collapse-whitespace --remove-comments --minify-css --minify-js index.html -o dist/index.html

# 压缩CSS
npm install -g clean-css-cli
cleancss -o dist/styles.min.css styles.css

# 压缩JavaScript
npm install -g terser
terser script.js -o dist/script.min.js -c -m
```

### 9.2 CDN配置
```javascript
// 资源CDN化
const CDN_BASE = 'https://cdn.example.com';

function getCDNUrl(path) {
  return `${CDN_BASE}${path}`;
}

// 动态加载资源
function loadResource(type, src) {
  return new Promise((resolve, reject) => {
    const element = type === 'css' 
      ? document.createElement('link')
      : document.createElement('script');
    
    if (type === 'css') {
      element.rel = 'stylesheet';
      element.href = src;
    } else {
      element.src = src;
    }
    
    element.onload = resolve;
    element.onerror = reject;
    
    document.head.appendChild(element);
  });
}
```

### 9.3 性能监控部署
```javascript
// 生产环境性能监控
if (process.env.NODE_ENV === 'production') {
  // Google Analytics
  gtag('config', 'GA_MEASUREMENT_ID');
  
  // 错误监控
  window.addEventListener('error', (event) => {
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    });
  });
}
```

## 10. 最佳实践总结

### 10.1 开发原则
1. **移动优先**: 先设计移动端，再适配桌面端
2. **性能优先**: 每个功能都要考虑性能影响
3. **用户体验**: 优先保证核心功能的流畅体验
4. **渐进增强**: 基础功能在所有设备上可用

### 10.2 测试策略
1. **真机测试**: 在实际设备上测试核心功能
2. **网络测试**: 测试弱网络环境下的表现
3. **电池测试**: 监控应用对设备电池的影响
4. **兼容性测试**: 覆盖主流设备和浏览器

### 10.3 持续优化
1. **性能监控**: 持续监控关键性能指标
2. **用户反馈**: 收集并分析用户使用反馈
3. **数据分析**: 基于数据优化用户体验
4. **技术更新**: 跟进新技术和最佳实践