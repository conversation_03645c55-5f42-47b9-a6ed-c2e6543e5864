# TikTok任务平台 - 数据库设计文档

> **文档版本**: v1.0.0  
> **创建日期**: 2024-08-01  
> **架构师**: Senior Solution Architect  
> **专项**: 数据库架构设计与数据模型

## 📋 目录

- [1. 数据库架构概览](#1-数据库架构概览)
- [2. 用户系统表设计](#2-用户系统表设计)
- [3. 钱包系统表设计](#3-钱包系统表设计)
- [4. 任务系统表设计](#4-任务系统表设计)
- [5. 推荐系统表设计](#5-推荐系统表设计)
- [6. VIP系统表设计](#6-vip系统表设计)
- [7. 系统管理表设计](#7-系统管理表设计)
- [8. 索引优化策略](#8-索引优化策略)

## 1. 数据库架构概览

### 1.1 技术选型

```sql
-- 数据库配置
Database: MySQL 8.0+
Character Set: utf8mb4
Collation: utf8mb4_unicode_ci
Engine: InnoDB
Time Zone: UTC

-- 主要特性
- ACID事务支持
- 行级锁定
- 外键约束
- 分区表支持
- JSON数据类型
```

### 1.2 数据库分层架构

```javascript
const databaseArchitecture = {
  core: {
    description: '核心业务数据',
    tables: ['users', 'user_wallets', 'tasks', 'task_orders'],
    characteristics: '高一致性要求，主从复制'
  },
  
  analytics: {
    description: '分析统计数据',
    tables: ['user_statistics', 'task_statistics', 'commission_statistics'],
    characteristics: '读多写少，可适当延迟'
  },
  
  logs: {
    description: '日志审计数据',
    tables: ['audit_logs', 'security_logs', 'operation_logs'],
    characteristics: '写入频繁，定期归档'
  },
  
  cache: {
    description: '缓存辅助数据',
    storage: 'Redis',
    usage: '会话、计数器、临时数据'
  }
};
```

### 1.3 命名规范

```sql
-- 表命名规范
- 使用复数形式: users, tasks, orders
- 下划线分隔: user_wallets, task_orders
- 关联表: user_referrals, task_categories

-- 字段命名规范
- 主键: id (BIGINT AUTO_INCREMENT)
- 外键: {table}_id (如 user_id, task_id)
- 时间字段: created_at, updated_at, deleted_at
- 状态字段: status, is_active, is_deleted
- 金额字段: DECIMAL(18,8) 统一精度
```

## 2. 用户系统表设计

### 2.1 用户基础表

```sql
-- 用户主表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    phone_encrypted VARCHAR(255) COMMENT '加密手机号',
    email VARCHAR(100) COMMENT '邮箱',
    email_encrypted VARCHAR(255) COMMENT '加密邮箱',
    
    -- 密码相关
    password_hash VARCHAR(255) NOT NULL COMMENT '登录密码哈希',
    payment_password_hash VARCHAR(255) COMMENT '支付密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    
    -- 基础信息
    avatar VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(50) COMMENT '真实姓名',
    real_name_encrypted VARCHAR(255) COMMENT '加密真实姓名',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0未知 1男 2女',
    birthday DATE COMMENT '生日',
    
    -- 推荐关系
    referral_code VARCHAR(20) UNIQUE NOT NULL COMMENT '推荐码',
    parent_id BIGINT COMMENT '推荐人ID',
    referral_path VARCHAR(500) COMMENT '推荐路径',
    
    -- VIP信息
    vip_level TINYINT DEFAULT 0 COMMENT 'VIP等级',
    vip_expire_time DATETIME COMMENT 'VIP过期时间',
    
    -- 状态信息
    status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active' COMMENT '账户状态',
    is_verified TINYINT DEFAULT 0 COMMENT '是否实名认证',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 索引
    INDEX idx_phone (phone),
    INDEX idx_referral_code (referral_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_vip_level (vip_level),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户主表';
```

### 2.2 用户扩展信息表

```sql
-- 用户详细信息表
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 身份信息
    id_card VARCHAR(255) COMMENT '加密身份证号',
    id_card_front_url VARCHAR(500) COMMENT '身份证正面照',
    id_card_back_url VARCHAR(500) COMMENT '身份证背面照',
    
    -- 联系信息
    address VARCHAR(500) COMMENT '联系地址',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    
    -- 社交媒体
    tiktok_username VARCHAR(100) COMMENT 'TikTok用户名',
    tiktok_profile_url VARCHAR(500) COMMENT 'TikTok主页链接',
    
    -- 偏好设置
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言偏好',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    notification_settings JSON COMMENT '通知设置',
    
    -- 统计信息
    total_tasks_completed INT DEFAULT 0 COMMENT '累计完成任务数',
    total_earnings DECIMAL(18,8) DEFAULT 0 COMMENT '累计收益',
    total_referrals INT DEFAULT 0 COMMENT '累计推荐人数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_tiktok_username (tiktok_username),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户详细信息表';
```

### 2.3 用户会话表

```sql
-- 用户会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(128) UNIQUE NOT NULL COMMENT '会话ID',
    refresh_token VARCHAR(255) UNIQUE NOT NULL COMMENT '刷新令牌',
    
    -- 设备信息
    device_id VARCHAR(128) COMMENT '设备ID',
    device_type VARCHAR(20) COMMENT '设备类型',
    device_name VARCHAR(100) COMMENT '设备名称',
    user_agent TEXT COMMENT '用户代理',
    
    -- 网络信息
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(100) COMMENT '地理位置',
    
    -- 会话状态
    is_active TINYINT DEFAULT 1 COMMENT '是否活跃',
    last_activity_at DATETIME COMMENT '最后活动时间',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';
```

## 3. 钱包系统表设计

### 3.1 用户钱包表

```sql
-- 用户钱包表
CREATE TABLE user_wallets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 余额信息
    available_balance DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '可用余额',
    frozen_balance DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '冻结余额',
    
    -- 统计信息
    total_deposits DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '累计充值',
    total_withdrawals DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '累计提现',
    total_earnings DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '累计收益',
    total_commissions DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '累计佣金',
    
    -- 限额信息
    daily_withdrawal_limit DECIMAL(18,8) DEFAULT 1000.00000000 COMMENT '日提现限额',
    daily_withdrawn_amount DECIMAL(18,8) DEFAULT 0.00000000 COMMENT '今日已提现',
    last_withdrawal_date DATE COMMENT '最后提现日期',
    
    -- 安全信息
    payment_password_attempts INT DEFAULT 0 COMMENT '支付密码错误次数',
    payment_password_locked_until DATETIME COMMENT '支付密码锁定到',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_available_balance (available_balance),
    INDEX idx_total_earnings (total_earnings),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户钱包表';
```

### 3.2 平台收款地址表

```sql
-- 平台收款地址表
CREATE TABLE platform_addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    network VARCHAR(20) NOT NULL COMMENT '网络类型: TRC20, ERC20',
    address VARCHAR(100) NOT NULL COMMENT '收款地址',
    private_key_encrypted TEXT COMMENT '加密私钥',
    contract_address VARCHAR(100) COMMENT 'USDT合约地址',
    
    -- 状态信息
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    daily_limit DECIMAL(18,8) DEFAULT 0 COMMENT '日收款限额',
    current_balance DECIMAL(18,8) DEFAULT 0 COMMENT '当前余额',
    
    -- 统计信息
    total_received DECIMAL(18,8) DEFAULT 0 COMMENT '累计收款',
    transaction_count INT DEFAULT 0 COMMENT '交易次数',
    last_transaction_at DATETIME COMMENT '最后交易时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_network_address (network, address),
    INDEX idx_network (network),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台收款地址表';
```

### 3.3 充值记录表

```sql
-- 充值记录表
CREATE TABLE deposit_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 交易信息
    tx_hash VARCHAR(100) UNIQUE NOT NULL COMMENT '交易哈希',
    network VARCHAR(20) NOT NULL COMMENT '网络类型',
    from_address VARCHAR(100) COMMENT '发送地址',
    to_address VARCHAR(100) COMMENT '接收地址',
    
    -- 金额信息
    amount DECIMAL(18,8) NOT NULL COMMENT '实际充值金额',
    encoded_amount DECIMAL(18,8) NOT NULL COMMENT '编码金额(含用户ID)',
    fee DECIMAL(18,8) DEFAULT 0 COMMENT '网络手续费',
    
    -- 确认信息
    block_number BIGINT COMMENT '区块高度',
    confirmations INT DEFAULT 0 COMMENT '当前确认数',
    required_confirmations INT DEFAULT 12 COMMENT '需要确认数',
    
    -- 状态信息
    status ENUM('pending', 'confirmed', 'failed', 'expired') DEFAULT 'pending' COMMENT '状态',
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    expired_at TIMESTAMP NULL COMMENT '过期时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_tx_hash (tx_hash),
    INDEX idx_status (status),
    INDEX idx_network (network),
    INDEX idx_created_at (created_at),
    INDEX idx_block_number (block_number),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值记录表';

### 3.4 提现记录表

```sql
-- 提现记录表
CREATE TABLE withdrawal_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',

    -- 交易信息
    tx_hash VARCHAR(100) COMMENT '交易哈希',
    network VARCHAR(20) NOT NULL COMMENT '网络类型',
    to_address VARCHAR(100) NOT NULL COMMENT '提现地址',

    -- 金额信息
    amount DECIMAL(18,8) NOT NULL COMMENT '提现金额',
    fee DECIMAL(18,8) NOT NULL COMMENT '手续费',
    actual_amount DECIMAL(18,8) NOT NULL COMMENT '实际到账金额',

    -- 审核信息
    admin_id BIGINT COMMENT '审核管理员ID',
    review_note TEXT COMMENT '审核备注',
    auto_approved TINYINT DEFAULT 0 COMMENT '是否自动审核',

    -- 状态信息
    status ENUM('pending', 'approved', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    failure_reason TEXT COMMENT '失败原因',

    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    approved_at TIMESTAMP NULL COMMENT '审核时间',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',

    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_network (network),
    INDEX idx_created_at (created_at),
    INDEX idx_admin_id (admin_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现记录表';
```

### 3.5 余额变动记录表

```sql
-- 余额变动记录表
CREATE TABLE balance_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',

    -- 变动信息
    type ENUM('deposit', 'withdrawal', 'task_reward', 'referral_commission', 'vip_upgrade', 'admin_adjustment') NOT NULL COMMENT '变动类型',
    amount DECIMAL(18,8) NOT NULL COMMENT '变动金额(正数增加,负数减少)',
    balance_before DECIMAL(18,8) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(18,8) NOT NULL COMMENT '变动后余额',

    -- 关联信息
    related_id BIGINT COMMENT '关联记录ID',
    related_type VARCHAR(50) COMMENT '关联记录类型',
    description VARCHAR(500) COMMENT '变动描述',

    -- 操作信息
    operator_id BIGINT COMMENT '操作员ID',
    operator_type ENUM('user', 'admin', 'system') DEFAULT 'system' COMMENT '操作员类型',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_related (related_type, related_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='余额变动记录表';
```

## 4. 任务系统表设计

### 4.1 任务主表

```sql
-- 任务主表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,

    -- 基础信息
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    instructions TEXT COMMENT '任务说明',
    type ENUM('like', 'follow', 'comment', 'share', 'view') NOT NULL COMMENT '任务类型',

    -- 目标信息
    target_url VARCHAR(1000) COMMENT '目标链接',
    target_platform VARCHAR(50) DEFAULT 'tiktok' COMMENT '目标平台',
    target_username VARCHAR(100) COMMENT '目标用户名',

    -- 奖励信息
    reward DECIMAL(10,4) NOT NULL COMMENT '任务奖励',
    bonus_rate DECIMAL(5,4) DEFAULT 0 COMMENT 'VIP奖励加成比例',

    -- 限制信息
    required_vip_level TINYINT DEFAULT 0 COMMENT '需要VIP等级',
    daily_limit INT DEFAULT 0 COMMENT '每日限制次数(0=无限制)',
    total_limit INT DEFAULT 0 COMMENT '总限制次数(0=无限制)',
    user_daily_limit INT DEFAULT 1 COMMENT '用户每日限制',

    -- 时间信息
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    estimated_duration INT DEFAULT 300 COMMENT '预计完成时间(秒)',

    -- 统计信息
    completed_count INT DEFAULT 0 COMMENT '已完成次数',
    success_rate DECIMAL(5,4) DEFAULT 1.0000 COMMENT '成功率',
    average_rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '平均评分',

    -- 状态信息
    status ENUM('draft', 'active', 'paused', 'completed', 'expired') DEFAULT 'draft' COMMENT '任务状态',
    priority INT DEFAULT 0 COMMENT '优先级',

    -- 创建信息
    created_by BIGINT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_vip_level (required_vip_level),
    INDEX idx_reward (reward),
    INDEX idx_priority (priority),
    INDEX idx_start_end_time (start_time, end_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务主表';
```

### 4.2 任务订单表

```sql
-- 任务订单表
CREATE TABLE task_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',

    -- 奖励信息
    reward_amount DECIMAL(10,4) NOT NULL COMMENT '奖励金额',
    bonus_amount DECIMAL(10,4) DEFAULT 0 COMMENT '加成金额',
    total_amount DECIMAL(10,4) NOT NULL COMMENT '总金额',

    -- 执行信息
    started_at DATETIME COMMENT '开始时间',
    submitted_at DATETIME COMMENT '提交时间',
    completed_at DATETIME COMMENT '完成时间',

    -- 提交内容
    submission_content TEXT COMMENT '提交内容',
    screenshot_urls JSON COMMENT '截图URLs',
    completion_proof TEXT COMMENT '完成证明',

    -- 审核信息
    reviewed_by BIGINT COMMENT '审核员ID',
    reviewed_at DATETIME COMMENT '审核时间',
    review_note TEXT COMMENT '审核备注',
    rating TINYINT COMMENT '评分(1-5)',

    -- 状态信息
    status ENUM('created', 'in_progress', 'submitted', 'approved', 'rejected', 'completed', 'expired') DEFAULT 'created' COMMENT '订单状态',
    failure_reason TEXT COMMENT '失败原因',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_user_status (user_id, status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务订单表';
```

## 5. 推荐系统表设计

### 5.1 推荐关系表

```sql
-- 推荐关系表
CREATE TABLE user_referrals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    referrer_id BIGINT NOT NULL COMMENT '推荐人ID',
    referee_id BIGINT NOT NULL COMMENT '被推荐人ID',
    level TINYINT NOT NULL COMMENT '推荐层级(1-3)',
    referral_code VARCHAR(20) NOT NULL COMMENT '使用的推荐码',

    -- 状态信息
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '关系状态',
    is_valid TINYINT DEFAULT 1 COMMENT '是否有效',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_referrer_referee (referrer_id, referee_id),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referee_id (referee_id),
    INDEX idx_level (level),
    INDEX idx_referral_code (referral_code),
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referee_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐关系表';
```

### 5.2 佣金记录表

```sql
-- 佣金记录表
CREATE TABLE commission_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,

    -- 基础信息
    referrer_id BIGINT NOT NULL COMMENT '推荐人ID',
    referee_id BIGINT NOT NULL COMMENT '被推荐人ID',
    source_user_id BIGINT NOT NULL COMMENT '产生佣金的用户ID',

    -- 佣金信息
    type ENUM('task_commission', 'vip_commission', 'deposit_commission') NOT NULL COMMENT '佣金类型',
    level TINYINT NOT NULL COMMENT '推荐层级',
    rate DECIMAL(5,4) NOT NULL COMMENT '佣金比例',
    original_amount DECIMAL(18,8) NOT NULL COMMENT '原始金额',
    commission_amount DECIMAL(18,8) NOT NULL COMMENT '佣金金额',

    -- 关联信息
    related_id BIGINT COMMENT '关联记录ID',
    related_type VARCHAR(50) COMMENT '关联记录类型',
    description VARCHAR(500) COMMENT '佣金描述',

    -- 状态信息
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending' COMMENT '支付状态',
    paid_at DATETIME COMMENT '支付时间',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referee_id (referee_id),
    INDEX idx_source_user_id (source_user_id),
    INDEX idx_type (type),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referee_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (source_user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='佣金记录表';
```

## 6. VIP系统表设计

### 6.1 VIP等级配置表

```sql
-- VIP等级配置表
CREATE TABLE vip_levels (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    level TINYINT UNIQUE NOT NULL COMMENT 'VIP等级',
    name VARCHAR(50) NOT NULL COMMENT '等级名称',
    price DECIMAL(10,4) NOT NULL COMMENT '升级价格',
    duration_days INT NOT NULL COMMENT '有效期天数',

    -- 特权配置
    daily_task_limit INT DEFAULT 10 COMMENT '每日任务限额',
    task_reward_bonus DECIMAL(5,4) DEFAULT 0 COMMENT '任务奖励加成',
    referral_bonus DECIMAL(5,4) DEFAULT 0 COMMENT '推荐佣金加成',
    withdrawal_limit DECIMAL(18,8) DEFAULT 1000 COMMENT '提现限额',

    -- 特权描述
    privileges JSON COMMENT '特权列表',
    description TEXT COMMENT '等级描述',

    -- 状态信息
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_level (level),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP等级配置表';
```

### 6.2 VIP升级记录表

```sql
-- VIP升级记录表
CREATE TABLE vip_upgrade_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',

    -- 升级信息
    from_level TINYINT NOT NULL COMMENT '原等级',
    to_level TINYINT NOT NULL COMMENT '目标等级',
    price DECIMAL(10,4) NOT NULL COMMENT '升级费用',
    duration_days INT NOT NULL COMMENT '有效期天数',

    -- 时间信息
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',

    -- 支付信息
    payment_method ENUM('balance', 'deposit') DEFAULT 'balance' COMMENT '支付方式',
    transaction_id VARCHAR(64) COMMENT '交易ID',

    -- 状态信息
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending' COMMENT '状态',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_end_time (end_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP升级记录表';
```

---

## 📋 数据库优化策略

### 分区表设计
```sql
-- 大数据量表分区策略
ALTER TABLE balance_logs PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 索引优化
```sql
-- 复合索引优化
CREATE INDEX idx_user_status_time ON task_orders(user_id, status, created_at);
CREATE INDEX idx_referrer_level_status ON commission_records(referrer_id, level, status);
```

---

**文档状态**: ✅ 数据库设计完成
**覆盖范围**: 用户、钱包、任务、推荐、VIP系统完整数据模型
**下次更新**: 根据业务发展补充统计分析表设计
```
