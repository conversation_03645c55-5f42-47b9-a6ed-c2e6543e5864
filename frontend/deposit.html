<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USDT充值 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .usdt-icon {
            background: linear-gradient(135deg, #26A17B 0%, #22C55E 100%);
        }
        
        .amount-btn {
            transition: all 0.3s ease;
        }
        
        .amount-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.05);
        }
        
        .qr-code {
            background: #f8fafc;
            border: 2px dashed #e2e8f0;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        @media (max-width: 768px) {
            .mobile-container {
                width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            .px-6 {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-6 py-4">
            <div class="flex items-center justify-between text-white">
                <button onclick="goBackToWallet()" class="p-2 -ml-2 hover:bg-white/20 rounded-full transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-lg font-semibold">USDT充值</h1>
                <button class="p-2 hover:bg-white/20 rounded-full transition-colors">
                    <i class="fas fa-question-circle text-lg"></i>
                </button>
            </div>
        </div>

        <!-- 当前余额显示 -->
        <div class="px-6 -mt-2 relative z-10">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">当前余额</p>
                        <div class="flex items-center">
                            <div class="usdt-icon w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">₮</span>
                            </div>
                            <span class="text-2xl font-bold text-gray-900">128.56</span>
                            <span class="text-lg ml-2 text-gray-600">USDT</span>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-gray-600 text-sm mb-1">今日充值</p>
                        <p class="text-lg font-semibold text-green-600">+100.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值金额选择 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-coins text-green-500 mr-2"></i>选择充值金额
                </h3>
                
                <!-- 快捷金额按钮 -->
                <div class="grid grid-cols-3 gap-3 mb-4">
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="selectAmount(50)">
                        50 USDT
                    </button>
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="selectAmount(100)">
                        100 USDT
                    </button>
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="selectAmount(200)">
                        200 USDT
                    </button>
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="selectAmount(500)">
                        500 USDT
                    </button>
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="selectAmount(1000)">
                        1000 USDT
                    </button>
                    <button class="amount-btn bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200" onclick="showCustomAmount()">
                        自定义
                    </button>
                </div>
                
                <!-- 自定义金额输入 -->
                <div id="customAmountInput" class="hidden mb-4">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">自定义金额</label>
                    <div class="relative">
                        <input type="number" id="customAmount" placeholder="请输入充值金额" 
                               class="w-full px-4 py-3 pr-16 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg font-semibold">
                        <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">USDT</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">最小充值金额：10 USDT，最大充值金额：10,000 USDT</p>
                </div>
                
                <!-- 选中的金额显示 -->
                <div id="selectedAmountDisplay" class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 hidden">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-900 text-sm font-medium">充值金额</p>
                            <p class="text-2xl font-bold text-green-600" id="selectedAmountText">100 USDT</p>
                        </div>
                        <div class="text-right">
                            <p class="text-green-900 text-sm font-medium">预计到账</p>
                            <p class="text-lg font-semibold text-green-600" id="expectedAmountText">100 USDT</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值方式选择 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-credit-card text-blue-500 mr-2"></i>选择充值方式
                </h3>
                
                <div class="space-y-3">
                    <!-- USDT TRC20 -->
                    <div class="payment-method border-2 border-indigo-200 bg-indigo-50 rounded-xl p-4 cursor-pointer" onclick="selectPaymentMethod('trc20')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">₮</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">USDT (TRC20)</h4>
                                    <p class="text-sm text-gray-600">推荐 • 手续费低 • 到账快</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="w-5 h-5 border-2 border-indigo-500 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-indigo-500 rounded-full" style="display: block;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- USDT ERC20 -->
                    <div class="payment-method border-2 border-gray-200 bg-gray-50 rounded-xl p-4 cursor-pointer" onclick="selectPaymentMethod('erc20')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">Ξ</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">USDT (ERC20)</h4>
                                    <p class="text-sm text-gray-600">以太坊网络 • 手续费较高</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-indigo-500 rounded-full" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值地址和二维码 -->
        <div class="px-6 mb-6" id="depositAddressSection" style="display: none;">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-qrcode text-purple-500 mr-2"></i>充值地址
                </h3>
                
                <!-- 二维码 -->
                <div class="text-center mb-6">
                    <div class="qr-code w-48 h-48 mx-auto rounded-xl flex items-center justify-center mb-4">
                        <div class="text-center">
                            <i class="fas fa-qrcode text-6xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-500">扫码充值</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600">使用支持TRC20的钱包扫码转账</p>
                </div>
                
                <!-- 充值地址 -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center min-w-0 flex-1 mr-3">
                            <i class="fas fa-wallet text-gray-400 mr-2 flex-shrink-0"></i>
                            <div class="min-w-0 flex-1">
                                <p class="text-xs text-gray-500 mb-1">TRC20充值地址</p>
                                <p class="text-sm text-gray-900 font-mono break-all" id="depositAddress">TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE</p>
                            </div>
                        </div>
                        <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors flex-shrink-0 ml-2" onclick="copyAddress()">
                            <i class="fas fa-copy mr-1"></i>复制
                        </button>
                    </div>
                </div>
                
                <!-- 重要提示 -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-3 mt-0.5 flex-shrink-0"></i>
                        <div>
                            <p class="text-red-900 text-sm font-medium mb-2">重要提示</p>
                            <ul class="text-red-800 text-sm space-y-1">
                                <li>• 请确保使用TRC20网络转账</li>
                                <li>• 最小充值金额：10 USDT</li>
                                <li>• 充值后1-3个区块确认到账</li>
                                <li>• 请勿向此地址转入其他币种</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 确认充值按钮 -->
                <button 
                    id="confirmDepositBtn"
                    onclick="confirmDeposit()" 
                    class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold py-4 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                >
                    <i class="fas fa-plus-circle mr-2"></i>
                    <span id="confirmDepositText">请选择充值金额</span>
                </button>
            </div>
        </div>

        <!-- 充值记录 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-history text-gray-500 mr-2"></i>最近充值记录
                    </h3>
                    <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors">
                        查看全部
                    </button>
                </div>
                
                <div class="space-y-3">
                    <!-- 充值记录项 -->
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-arrow-down text-green-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">USDT充值</p>
                                <p class="text-sm text-gray-500">TRC20网络</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-green-600">+100.00 USDT</p>
                            <p class="text-xs text-gray-500">已确认</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-clock text-yellow-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">USDT充值</p>
                                <p class="text-sm text-gray-500">TRC20网络</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-yellow-600">+50.00 USDT</p>
                            <p class="text-xs text-gray-500">确认中</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 客服帮助 -->
        <div class="px-6 pb-6">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-headset text-blue-500 mr-3 mt-0.5"></i>
                    <div class="flex-1">
                        <p class="text-blue-900 text-sm font-medium mb-1">充值遇到问题？</p>
                        <p class="text-blue-800 text-sm mb-3">我们的客服团队24小时为您服务</p>
                        <div class="flex space-x-3">
                            <button class="bg-blue-500 text-white text-xs px-3 py-1.5 rounded-full hover:bg-blue-600 transition-colors">
                                <i class="fas fa-comments mr-1"></i>在线客服
                            </button>
                            <button class="bg-white text-blue-600 text-xs px-3 py-1.5 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors">
                                <i class="fas fa-phone mr-1"></i>电话客服
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        let selectedAmount = 0;
        let selectedPaymentMethod = 'trc20';
        
        // 选择充值金额
        function selectAmount(amount) {
            selectedAmount = amount;
            
            // 更新按钮状态
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 隐藏自定义输入
            document.getElementById('customAmountInput').classList.add('hidden');
            
            // 显示选中金额
            updateSelectedAmount(amount);
            
            // 更新确认充值按钮
            updateConfirmButton(amount);
        }
        
        // 显示自定义金额输入
        function showCustomAmount() {
            // 清除其他按钮的选中状态
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 显示自定义输入
            document.getElementById('customAmountInput').classList.remove('hidden');
            document.getElementById('customAmount').focus();
            
            // 隐藏选中金额显示
            document.getElementById('selectedAmountDisplay').classList.add('hidden');
        }
        
        // 自定义金额输入监听
        document.getElementById('customAmount').addEventListener('input', function() {
            const amount = parseFloat(this.value);
            if (amount >= 10) {
                selectedAmount = amount;
                updateSelectedAmount(amount);
                updateConfirmButton(amount);
            } else {
                document.getElementById('selectedAmountDisplay').classList.add('hidden');
                updateConfirmButton(0);
            }
        });
        
        // 更新选中金额显示
        function updateSelectedAmount(amount) {
            document.getElementById('selectedAmountText').textContent = amount + ' USDT';
            document.getElementById('expectedAmountText').textContent = amount + ' USDT';
            document.getElementById('selectedAmountDisplay').classList.remove('hidden');
            
            // 如果选择了金额，显示充值地址
            if (amount > 0) {
                document.getElementById('depositAddressSection').style.display = 'block';
            }
        }
        
        // 选择支付方式
        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;
            
            // 更新支付方式选中状态
            document.querySelectorAll('.payment-method').forEach(item => {
                item.classList.remove('border-indigo-200', 'bg-indigo-50');
                item.classList.add('border-gray-200', 'bg-gray-50');
                
                // 隐藏所有单选按钮的内部圆点
                const radioInner = item.querySelector('.w-5 .w-3');
                if (radioInner) {
                    radioInner.style.display = 'none';
                }
                
                // 更新单选按钮外圈颜色为灰色
                const radioOuter = item.querySelector('.w-5.h-5');
                if (radioOuter) {
                    radioOuter.classList.remove('border-indigo-500');
                    radioOuter.classList.add('border-gray-300');
                }
            });
            
            // 设置选中的支付方式
            const selectedMethod = event.currentTarget;
            selectedMethod.classList.remove('border-gray-200', 'bg-gray-50');
            selectedMethod.classList.add('border-indigo-200', 'bg-indigo-50');
            
            // 显示选中项的单选按钮内部圆点
            const radioInner = selectedMethod.querySelector('.w-5 .w-3');
            if (radioInner) {
                radioInner.style.display = 'block';
            }
            
            // 更新选中项的单选按钮外圈颜色为蓝色
            const radioOuter = selectedMethod.querySelector('.w-5.h-5');
            if (radioOuter) {
                radioOuter.classList.remove('border-gray-300');
                radioOuter.classList.add('border-indigo-500');
            }
            
            // 更新充值地址
            updateDepositAddress(method);
        }
        
        // 更新充值地址
        function updateDepositAddress(method) {
            const addresses = {
                'trc20': 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
                'erc20': '******************************************'
            };
            
            document.getElementById('depositAddress').textContent = addresses[method];
        }
        
        // 复制地址
        function copyAddress() {
            const address = document.getElementById('depositAddress').textContent;
            navigator.clipboard.writeText(address).then(() => {
                // 显示复制成功提示
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check mr-1"></i>已复制';
                btn.classList.add('text-green-600');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('text-green-600');
                    btn.classList.add('text-indigo-600');
                }, 2000);
            });
        }
        
        // 更新确认充值按钮状态
        function updateConfirmButton(amount) {
            const button = document.getElementById('confirmDepositBtn');
            const buttonText = document.getElementById('confirmDepositText');
            
            if (amount >= 10) {
                button.disabled = false;
                buttonText.textContent = `确认充值 ${amount} USDT`;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                button.disabled = true;
                buttonText.textContent = amount > 0 ? '最小充值金额为10 USDT' : '请选择充值金额';
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // 确认充值
        function confirmDeposit() {
            if (selectedAmount < 10) {
                alert('最小充值金额为10 USDT');
                return;
            }

            // 显示充值确认对话框
            showDepositConfirmModal(selectedAmount);
        }

        // 显示充值确认对话框
        function showDepositConfirmModal(amount) {
            const modal = createDepositModal(amount);
            document.body.appendChild(modal);
            
            // 显示动画
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
        }

        // 创建充值确认模态框
        function createDepositModal(amount) {
            // 获取当前选择的支付方式信息
            const networkInfo = {
                'trc20': { name: 'TRC20', desc: 'TRON网络' },
                'erc20': { name: 'ERC20', desc: '以太坊网络' }
            };
            const currentNetwork = networkInfo[selectedPaymentMethod] || networkInfo['trc20'];
            
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-all duration-300';
            modal.innerHTML = `
                <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
                    <div class="text-center">
                        <!-- 充值图标 -->
                        <div class="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-plus-circle text-white text-3xl"></i>
                        </div>
                        
                        <!-- 标题 -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3">确认充值申请</h3>
                        
                        <!-- 充值信息 -->
                        <div class="bg-gray-50 rounded-2xl p-4 mb-6">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">充值金额</span>
                                <span class="font-bold text-lg text-green-600">${amount} USDT</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">网络类型</span>
                                <span class="font-bold text-lg">${currentNetwork.name}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">预计到账</span>
                                <span class="font-bold text-lg text-blue-600">${amount} USDT</span>
                            </div>
                        </div>
                        
                        <!-- 提示信息 -->
                        <div class="bg-blue-50 rounded-2xl p-4 mb-4">
                            <p class="text-blue-800 text-sm">
                                <i class="fas fa-info-circle mr-2"></i>
                                请向上方地址转入 <strong>${amount} USDT</strong>，转账完成后系统将自动确认到账。
                            </p>
                        </div>
                        
                        <!-- 上传转账截图 -->
                        <div class="bg-gray-50 rounded-2xl p-4 mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-medium text-gray-800">
                                    <i class="fas fa-image text-purple-500 mr-2"></i>上传转账截图 (可选)
                                </h4>
                                <span class="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">加快确认</span>
                            </div>
                            
                            <!-- 文件上传区域 -->
                            <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center cursor-pointer hover:border-purple-400 hover:bg-purple-50 transition-all" onclick="document.getElementById('screenshotInput').click()">
                                <div id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600 text-sm mb-1">点击上传转账截图</p>
                                    <p class="text-xs text-gray-500">支持 JPG、PNG 格式，最大 5MB</p>
                                </div>
                                
                                <!-- 预览区域 -->
                                <div id="imagePreview" class="hidden">
                                    <img id="previewImg" class="max-w-full h-32 object-cover rounded-lg mx-auto mb-2">
                                    <p class="text-sm text-green-600 font-medium">
                                        <i class="fas fa-check-circle mr-1"></i>截图已上传
                                    </p>
                                    <button type="button" onclick="removeImage(event)" class="text-xs text-red-500 hover:text-red-700 mt-1">
                                        <i class="fas fa-times mr-1"></i>移除图片
                                    </button>
                                </div>
                            </div>
                            
                            <input type="file" id="screenshotInput" accept="image/*" class="hidden" onchange="handleImageUpload(event)">
                            
                            <p class="text-xs text-gray-500 mt-2">
                                <i class="fas fa-lightbulb text-yellow-500 mr-1"></i>
                                提示：上传转账截图可以帮助客服更快确认您的充值
                            </p>
                        </div>
                        
                        <!-- 按钮组 -->
                        <div class="flex space-x-3">
                            <button onclick="closeDepositModal()" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                                取消
                            </button>
                            <button onclick="submitDepositOrder(${amount})" class="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all">
                                确认申请
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // 点击背景关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    closeDepositModal();
                }
            };
            
            return modal;
        }

        // 提交充值订单
        function submitDepositOrder(amount) {
            // 检查是否上传了截图
            const screenshotInput = document.getElementById('screenshotInput');
            const hasScreenshot = screenshotInput && screenshotInput.files.length > 0;
            
            // 模拟提交充值订单（包含截图信息）
            const orderData = {
                amount: amount,
                paymentMethod: selectedPaymentMethod,
                screenshot: hasScreenshot ? screenshotInput.files[0] : null,
                timestamp: new Date().toISOString()
            };
            
            console.log('提交充值订单:', orderData);
            
            closeDepositModal();
            
            // 显示成功提示
            showDepositSuccessModal(amount, hasScreenshot);
            
            // 模拟添加到充值记录
            addDepositRecord(amount, hasScreenshot);
        }

        // 显示充值成功提示
        function showDepositSuccessModal(amount, hasScreenshot = false) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-all duration-300';
            
            const screenshotInfo = hasScreenshot ? 
                '<div class="bg-purple-50 rounded-lg p-3 mt-3"><p class="text-purple-800 text-sm"><i class="fas fa-image text-purple-500 mr-2"></i>转账截图已上传，将优先处理</p></div>' : 
                '';
            
            modal.innerHTML = `
                <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
                    <div class="text-center">
                        <!-- 成功图标 -->
                        <div class="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                            <i class="fas fa-check text-white text-3xl"></i>
                        </div>
                        
                        <!-- 成功标题 -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3">🎉 申请提交成功！</h3>
                        
                        <!-- 成功信息 -->
                        <div class="bg-green-50 rounded-2xl p-4 mb-6">
                            <p class="text-green-800 font-medium">
                                充值申请已提交成功！<br>
                                金额：<strong>${amount} USDT</strong><br>
                                请按照上方地址进行转账，系统将自动确认到账。
                            </p>
                            ${screenshotInfo}
                        </div>
                        
                        <!-- 关闭按钮 -->
                        <button onclick="closeDepositModal()" class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg">
                            <i class="fas fa-check mr-2"></i>
                            知道了
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 显示动画
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }
            
            // 检查文件大小 (5MB = 5 * 1024 * 1024 bytes)
            if (file.size > 5 * 1024 * 1024) {
                alert('图片大小不能超过 5MB');
                return;
            }
            
            // 读取并预览图片
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImg = document.getElementById('previewImg');
                const uploadArea = document.getElementById('uploadArea');
                const imagePreview = document.getElementById('imagePreview');
                
                previewImg.src = e.target.result;
                uploadArea.classList.add('hidden');
                imagePreview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
        
        // 移除上传的图片
        function removeImage(event) {
            event.stopPropagation();
            const uploadArea = document.getElementById('uploadArea');
            const imagePreview = document.getElementById('imagePreview');
            const screenshotInput = document.getElementById('screenshotInput');
            
            uploadArea.classList.remove('hidden');
            imagePreview.classList.add('hidden');
            screenshotInput.value = '';
        }

        // 关闭充值模态框
        function closeDepositModal() {
            const modals = document.querySelectorAll('.fixed.inset-0.bg-black');
            modals.forEach(modal => {
                modal.querySelector('.bg-white').classList.remove('scale-100');
                modal.querySelector('.bg-white').classList.add('scale-95');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            });
        }

        // 添加充值记录
        function addDepositRecord(amount, hasScreenshot = false) {
            const recordsContainer = document.querySelector('.space-y-3');
            if (recordsContainer) {
                const newRecord = document.createElement('div');
                const screenshotBadge = hasScreenshot ? '<span class="text-xs bg-purple-100 text-purple-600 px-2 py-0.5 rounded-full ml-1">📷</span>' : '';
                
                // 获取当前选择的网络类型
                const networkDisplay = selectedPaymentMethod === 'erc20' ? 'ERC20网络' : 'TRC20网络';
                
                newRecord.className = 'flex items-center justify-between p-3 bg-yellow-50 rounded-lg';
                newRecord.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-yellow-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900 flex items-center">
                                USDT充值 ${screenshotBadge}
                            </p>
                            <p class="text-sm text-gray-500">${networkDisplay}${hasScreenshot ? ' • 已上传截图' : ''}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-yellow-600">+${amount} USDT</p>
                        <p class="text-xs text-gray-500">${hasScreenshot ? '优先处理中' : '处理中'}</p>
                    </div>
                `;
                recordsContainer.insertBefore(newRecord, recordsContainer.firstChild);
            }
        }


        // 返回钱包页面
        function goBackToWallet() {
            // 直接返回到钱包Tab页面
            window.location.href = 'index.html#wallet';
        }
        
        // 页面加载时默认选择TRC20
        document.addEventListener('DOMContentLoaded', function() {
            selectPaymentMethod('trc20');
        });
    </script>
</body>
</html>