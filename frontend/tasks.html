<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务大厅 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }

        .task-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .earning-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 顶部导航和用户信息 -->
    <div class="bg-gradient-to-r from-indigo-500 to-violet-500 px-6 pt-6 pb-4">
        <!-- 导航栏 -->
        <div class="flex items-center justify-between text-white mb-4">
            <button onclick="history.back()" class="p-2 -ml-2 hover:bg-white/20 rounded-full transition-colors">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-lg font-semibold">任务大厅</h1>
            <button class="p-2 hover:bg-white/20 rounded-full transition-colors">
                <i class="fas fa-search text-lg"></i>
            </button>
        </div>

        <!-- 用户信息 -->
        <div class="flex items-center justify-between text-white">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face"
                    alt="用户头像" class="w-12 h-12 rounded-full border-2 border-white/30">
                <div class="ml-3">
                    <h2 class="font-semibold">张小明</h2>
                    <div class="flex items-center">
                        <i class="fas fa-crown text-yellow-300 text-xs mr-1"></i>
                        <span class="text-sm opacity-90">VIP 1</span>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors">
                    <i class="fas fa-bell text-lg"></i>
                </button>
                <button class="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors">
                    <i class="fas fa-cog text-lg"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 收益统计卡片 -->
    <div class="px-6 -mt-6 relative z-10">
        <div class="earning-card rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">今日收益</h3>
                <button class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-chart-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-orange-600">¥68.50</p>
                    <p class="text-sm text-gray-600">今日收入</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">12</p>
                    <p class="text-sm text-gray-600">完成任务</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600">¥856.30</p>
                    <p class="text-sm text-gray-600">总收益</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷操作 -->
    <div class="px-6 mt-6">
        <div class="flex items-center space-x-4">
            <button
                class="flex-1 bg-gradient-to-r from-green-400 to-green-500 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:from-green-500 hover:to-green-600 transition-all">
                <i class="fas fa-calendar-check mr-2"></i>签到领奖
            </button>
            <button
                class="flex-1 bg-gradient-to-r from-blue-400 to-blue-500 text-white font-semibold py-3 px-4 rounded-xl shadow-lg hover:from-blue-500 hover:to-blue-600 transition-all">
                <i class="fas fa-gift mr-2"></i>新手任务
            </button>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="px-6 mt-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">推荐任务</h3>
            <span class="text-sm text-gray-500">更多任务</span>
        </div>

        <!-- 任务卡片1 -->
        <div class="task-card rounded-2xl p-6 mb-4 shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            onclick="goToTaskDetails('like', '2.50', 'TikTok点赞任务', '为指定视频点赞')">
            <div class="flex items-start justify-between text-white">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <i class="fab fa-tiktok text-2xl mr-3"></i>
                        <div>
                            <h4 class="font-semibold">TikTok点赞任务</h4>
                            <p class="text-sm opacity-90">为指定视频点赞</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <div>
                            <p class="text-2xl font-bold">¥2.50</p>
                            <p class="text-sm opacity-80">单次奖励</p>
                        </div>
                        <button
                            class="bg-white/20 hover:bg-white/30 px-6 py-2 rounded-full font-semibold transition-colors"
                            onclick="event.stopPropagation(); goToTaskDetails('like', '2.50', 'TikTok点赞任务', '为指定视频点赞')">
                            立即开始
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务卡片2 -->
        <div class="bg-white rounded-2xl p-6 mb-4 shadow-lg border border-gray-100 cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            onclick="goToTaskDetails('follow', '5.00', 'TikTok关注任务', '关注指定用户账号')">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div
                            class="w-12 h-12 bg-gradient-to-r from-pink-400 to-red-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-heart text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">TikTok关注任务</h4>
                            <p class="text-sm text-gray-600">关注指定用户账号</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <div>
                            <p class="text-2xl font-bold text-indigo-600">¥5.00</p>
                            <p class="text-sm text-gray-500">单次奖励</p>
                        </div>
                        <button
                            class="bg-gradient-to-r from-indigo-500 to-violet-500 text-white px-6 py-2 rounded-full font-semibold hover:from-indigo-600 hover:to-violet-600 transition-all"
                            onclick="event.stopPropagation(); goToTaskDetails('follow', '5.00', 'TikTok关注任务', '关注指定用户账号')">
                            立即开始
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务卡片3 -->
        <div class="bg-white rounded-2xl p-6 mb-4 shadow-lg border border-gray-100 cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            onclick="showVipUpgrade()">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div
                            class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-comment text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">TikTok评论任务</h4>
                            <p class="text-sm text-gray-600">发表正面评论</p>
                        </div>
                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full ml-2">VIP专享</span>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <div>
                            <p class="text-2xl font-bold text-orange-600">¥8.00</p>
                            <p class="text-sm text-gray-500">单次奖励</p>
                        </div>
                        <button
                            class="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-6 py-2 rounded-full font-semibold hover:from-yellow-500 hover:to-orange-500 transition-all"
                            onclick="event.stopPropagation(); showVipUpgrade()">
                            升级VIP
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务卡片4 -->
        <div class="bg-white rounded-2xl p-6 mb-4 shadow-lg border border-gray-100 cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            onclick="goToTaskDetails('share', '3.50', 'TikTok分享任务', '分享视频到社交平台')">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div
                            class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-share text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">TikTok分享任务</h4>
                            <p class="text-sm text-gray-600">分享视频到社交平台</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <div>
                            <p class="text-2xl font-bold text-green-600">¥3.50</p>
                            <p class="text-sm text-gray-500">单次奖励</p>
                        </div>
                        <button
                            class="bg-gradient-to-r from-green-400 to-emerald-400 text-white px-6 py-2 rounded-full font-semibold hover:from-green-500 hover:to-emerald-500 transition-all"
                            onclick="event.stopPropagation(); goToTaskDetails('share', '3.50', 'TikTok分享任务', '分享视频到社交平台')">
                            立即开始
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-indigo-600">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs font-medium">钱包</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs font-medium">VIP</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">推荐</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 跳转到任务详情页面
        function goToTaskDetails(taskType, reward, title, description) {
            // 将任务信息存储到localStorage，供任务详情页面使用
            const taskData = {
                type: taskType,
                reward: reward,
                title: title,
                description: description,
                timestamp: Date.now()
            };

            localStorage.setItem('currentTask', JSON.stringify(taskData));

            // 跳转到任务详情页面
            window.location.href = 'task-details.html';
        }

        // 显示VIP升级提示
        function showVipUpgrade() {
            if (confirm('此任务需要VIP 2及以上等级才能参与。\n是否前往VIP中心升级？')) {
                window.location.href = 'vip.html';
            }
        }

        // 页面加载时的动画效果
        document.addEventListener('DOMContentLoaded', function () {
            const taskCards = document.querySelectorAll('.task-card, .bg-white');
            taskCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 添加任务卡片点击反馈效果
        document.querySelectorAll('[onclick*="goToTaskDetails"]').forEach(card => {
            card.addEventListener('mousedown', function () {
                this.style.transform = 'scale(0.98)';
            });

            card.addEventListener('mouseup', function () {
                this.style.transform = 'scale(1)';
            });

            card.addEventListener('mouseleave', function () {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>

</html>