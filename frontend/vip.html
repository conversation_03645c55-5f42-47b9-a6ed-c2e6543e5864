<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP特权 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .vip-gradient {
            background: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #ffed4a 100%);
        }
        .vip-card {
            background: linear-gradient(135deg, #2d1b69 0%, #764ba2 50%, #667eea 100%);
        }
        .premium-shine {
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="vip-gradient shadow-lg">
        <div class="flex items-center justify-between p-4 text-black">
            <h1 class="text-xl font-bold flex items-center">
                <i class="fas fa-crown mr-2 text-yellow-800"></i>
                VIP特权中心
            </h1>
            <button class="p-2 text-black/70 hover:text-black">
                <i class="fas fa-question-circle text-lg"></i>
            </button>
        </div>
    </div>

    <!-- 当前VIP状态 -->
    <div class="p-6">
        <div class="vip-card rounded-2xl p-6 text-white mb-6 shadow-xl relative overflow-hidden">
            <div class="premium-shine absolute inset-0 w-full h-full"></div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-crown text-2xl text-white"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold">VIP 1</h2>
                            <p class="opacity-90">青铜会员</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-semibold">距离升级</p>
                        <p class="text-sm opacity-80">还需 15 天</p>
                    </div>
                </div>

                <!-- VIP进度条 -->
                <div class="bg-white/20 rounded-full p-1 mb-4">
                    <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full h-3 relative overflow-hidden">
                        <div class="w-3/4 bg-white/30 rounded-full h-full"></div>
                        <div class="absolute inset-0 premium-shine"></div>
                    </div>
                </div>
                <p class="text-center text-sm opacity-90">当前经验值：750/1000</p>
            </div>
        </div>

        <!-- 我的特权 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-gem text-purple-500 mr-2"></i>我的特权
            </h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gradient-to-r from-green-100 to-emerald-100 p-4 rounded-xl">
                    <i class="fas fa-tasks text-green-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-green-800">每日任务</p>
                    <p class="text-sm text-green-600">15个/天</p>
                </div>
                <div class="bg-gradient-to-r from-blue-100 to-indigo-100 p-4 rounded-xl">
                    <i class="fas fa-coins text-blue-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-blue-800">收益加成</p>
                    <p class="text-sm text-blue-600">+10%</p>
                </div>
                <div class="bg-gradient-to-r from-purple-100 to-violet-100 p-4 rounded-xl">
                    <i class="fas fa-headset text-purple-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-purple-800">专属客服</p>
                    <p class="text-sm text-purple-600">优先处理</p>
                </div>
                <div class="bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-xl">
                    <i class="fas fa-gift text-yellow-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-yellow-800">生日礼品</p>
                    <p class="text-sm text-yellow-600">专属奖励</p>
                </div>
            </div>
        </div>

        <!-- VIP等级对比 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-star text-yellow-500 mr-2"></i>VIP等级对比
            </h3>
            
            <!-- VIP等级卡片 -->
            <div class="space-y-4">
                <!-- VIP 1 (当前) -->
                <div class="border-2 border-indigo-500 bg-indigo-50 rounded-xl p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-crown text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-indigo-900">VIP 1 - 青铜</h4>
                                <p class="text-sm text-indigo-700">当前等级</p>
                            </div>
                        </div>
                        <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">当前</span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">15个</p>
                            <p class="text-gray-600">每日任务</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">+10%</p>
                            <p class="text-gray-600">收益加成</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">优先</p>
                            <p class="text-gray-600">客服支持</p>
                        </div>
                    </div>
                </div>

                <!-- VIP 2 -->
                <div class="border border-gray-200 rounded-xl p-4 hover:border-purple-300 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-crown text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-purple-900">VIP 2 - 白银</h4>
                                <p class="text-sm text-purple-700">30 USDT</p>
                            </div>
                        </div>
                        <button class="bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-purple-600 transition-colors" onclick="upgradeVip(2, 30); console.log('VIP2按钮被点击');">
                            升级
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">25个</p>
                            <p class="text-gray-600">每日任务</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">+20%</p>
                            <p class="text-gray-600">收益加成</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">专属</p>
                            <p class="text-gray-600">任务池</p>
                        </div>
                    </div>
                </div>

                <!-- VIP 3 -->
                <div class="border border-gray-200 rounded-xl p-4 hover:border-yellow-400 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-crown text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-yellow-900">VIP 3 - 黄金</h4>
                                <p class="text-sm text-yellow-700">68 USDT</p>
                            </div>
                        </div>
                        <button class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-yellow-500 hover:to-yellow-700 transition-all" onclick="upgradeVip(3, 68); console.log('VIP3按钮被点击');">
                            升级
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">40个</p>
                            <p class="text-gray-600">每日任务</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">+35%</p>
                            <p class="text-gray-600">收益加成</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">1v1</p>
                            <p class="text-gray-600">专属客服</p>
                        </div>
                    </div>
                </div>

                <!-- VIP 4 -->
                <div class="border border-gray-200 rounded-xl p-4 hover:border-red-400 transition-colors relative overflow-hidden">
                    <div class="absolute top-0 right-0 bg-red-500 text-white px-2 py-1 text-xs font-bold">
                        推荐
                    </div>
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-crown text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-bold text-red-900">VIP 4 - 钻石</h4>
                                <p class="text-sm text-red-700">128 USDT</p>
                            </div>
                        </div>
                        <button class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-red-600 hover:to-pink-600 transition-all shadow-lg" onclick="upgradeVip(4, 128); console.log('VIP4按钮被点击');">
                            升级
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">60个</p>
                            <p class="text-gray-600">每日任务</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">+50%</p>
                            <p class="text-gray-600">收益加成</p>
                        </div>
                        <div class="text-center">
                            <p class="font-semibold text-gray-900">专属</p>
                            <p class="text-gray-600">高价任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP专属任务预览 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-diamond text-purple-500 mr-2"></i>VIP专属任务
            </h3>
            <div class="space-y-4">
                <div class="bg-gradient-to-r from-purple-100 to-indigo-100 rounded-xl p-4 border border-purple-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fab fa-tiktok text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-purple-900">VIP评论任务</h4>
                                <p class="text-sm text-purple-700">高质量评论</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-purple-600">¥12.00</p>
                            <span class="bg-purple-500 text-white px-2 py-1 rounded-full text-xs">VIP2+</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-xl p-4 border border-yellow-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-star text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-yellow-900">品牌合作任务</h4>
                                <p class="text-sm text-yellow-700">优质品牌推广</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-yellow-600">¥25.00</p>
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">VIP3+</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP升级优惠 -->
        <div class="bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl p-6 text-white shadow-lg">
            <div class="text-center">
                <i class="fas fa-fire text-3xl mb-3"></i>
                <h3 class="text-xl font-bold mb-2">限时升级优惠</h3>
                <p class="mb-4 opacity-90">现在升级VIP享受85折优惠</p>
                <div class="bg-white/20 rounded-xl p-4 mb-4">
                    <p class="text-2xl font-bold">立省 15%</p>
                    <p class="text-sm opacity-90">优惠还剩 2天 14小时</p>
                </div>
                <button class="bg-white text-red-500 font-bold py-3 px-8 rounded-xl hover:bg-gray-100 transition-colors shadow-lg">
                    <i class="fas fa-rocket mr-2"></i>立即升级
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs font-medium">钱包</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-indigo-600">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs font-medium">VIP</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">推荐</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </div>

    <!-- VIP升级确认对话框 -->
    <div id="vipUpgradeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div class="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center" id="vipUpgradeIcon">
                    <i class="fas fa-crown text-4xl"></i>
                </div>
                <h3 id="vipUpgradeTitle" class="text-xl font-bold text-gray-900 mb-3"></h3>
                <div class="bg-gray-50 rounded-2xl p-4 mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">升级费用</span>
                        <span id="vipUpgradeCost" class="font-bold text-lg"></span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">当前余额</span>
                        <span id="currentBalance" class="font-bold text-lg text-indigo-600">128.56 USDT</span>
                    </div>
                    <hr class="my-2">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">升级后余额</span>
                        <span id="afterBalance" class="font-bold text-lg text-green-600"></span>
                    </div>
                </div>
                <div class="space-y-3 mb-6 text-left">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span id="vipBenefit1"></span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span id="vipBenefit2"></span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span id="vipBenefit3"></span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button id="cancelUpgrade" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                        取消
                    </button>
                    <button id="confirmUpgrade" class="flex-1 text-white font-semibold py-3 px-6 rounded-xl transition-all">
                        确认升级
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 升级成功提示 -->
    <div id="upgradeSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-white text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">升级成功！</h3>
                <p id="upgradeSuccessText" class="text-gray-600 mb-6"></p>
                <button id="closeSuccessModal" class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all">
                    开始享受特权
                </button>
            </div>
        </div>
    </div>

    <script>
        // 用户数据模拟
        let userData = {
            balance: 128.56,
            vipLevel: 1,
            vipExpireTime: '2024-12-31 23:59:59'
        };

        // 调试函数
        function debug(message) {
            console.log('[VIP Debug]', message);
        }

        // 测试函数
        function testUpgrade() {
            debug('测试升级函数被调用');
            alert('升级功能正常！');
        }

        // VIP等级配置
        const vipConfigs = {
            2: {
                name: 'VIP 2 - 白银',
                cost: 30,
                color: 'from-purple-500 to-indigo-500',
                iconColor: 'bg-purple-500',
                benefits: ['25个每日任务', '+20%收益加成', '专属任务池']
            },
            3: {
                name: 'VIP 3 - 黄金',
                cost: 68,
                color: 'from-yellow-400 to-orange-500',
                iconColor: 'bg-gradient-to-r from-yellow-400 to-yellow-600',
                benefits: ['40个每日任务', '+35%收益加成', '1v1专属客服']
            },
            4: {
                name: 'VIP 4 - 钻石',
                cost: 128,
                color: 'from-red-500 to-pink-500',
                iconColor: 'bg-gradient-to-r from-red-500 to-pink-500',
                benefits: ['60个每日任务', '+50%收益加成', '专属高价任务']
            }
        };

        // VIP升级函数
        function upgradeVip(targetLevel, cost) {
            debug(`升级函数被调用: 目标等级=${targetLevel}, 费用=${cost}`);
            
            const config = vipConfigs[targetLevel];
            if (!config) {
                debug('配置错误：找不到VIP配置');
                alert('VIP配置错误，请刷新页面重试');
                return;
            }
            
            debug(`当前VIP等级: ${userData.vipLevel}, 当前余额: ${userData.balance}`);
            
            // 检查是否已经是更高等级
            if (userData.vipLevel >= targetLevel) {
                showNotification('您已经是更高等级的VIP了！', 'warning');
                return;
            }

            // 检查余额是否足够
            if (userData.balance < cost) {
                debug(`余额不足: 需要${cost}, 当前${userData.balance}`);
                showInsufficientBalanceModal(cost);
                return;
            }

            debug('显示升级确认对话框');
            // 显示升级确认对话框
            showUpgradeConfirmModal(targetLevel, config);
        }

        // 显示升级确认对话框
        function showUpgradeConfirmModal(targetLevel, config) {
            debug('开始显示升级确认对话框');
            
            const modal = document.getElementById('vipUpgradeModal');
            const icon = document.getElementById('vipUpgradeIcon');
            const title = document.getElementById('vipUpgradeTitle');
            const cost = document.getElementById('vipUpgradeCost');
            const afterBalance = document.getElementById('afterBalance');
            const confirmBtn = document.getElementById('confirmUpgrade');

            if (!modal) {
                debug('错误：找不到升级对话框元素');
                alert('页面元素加载错误，请刷新页面重试');
                return;
            }

            // 设置图标样式
            icon.className = `w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${config.iconColor}`;
            icon.innerHTML = '<i class="fas fa-crown text-white text-4xl"></i>';

            // 设置内容
            title.textContent = `升级到 ${config.name}`;
            cost.textContent = `${config.cost} USDT`;
            afterBalance.textContent = `${(userData.balance - config.cost).toFixed(2)} USDT`;

            // 设置特权
            document.getElementById('vipBenefit1').textContent = config.benefits[0];
            document.getElementById('vipBenefit2').textContent = config.benefits[1];
            document.getElementById('vipBenefit3').textContent = config.benefits[2];

            // 设置确认按钮样式
            confirmBtn.className = `flex-1 bg-gradient-to-r ${config.color} text-white font-semibold py-3 px-6 rounded-xl hover:opacity-90 transition-all`;
            
            // 绑定确认事件
            confirmBtn.onclick = () => confirmVipUpgrade(targetLevel, config);

            // 显示对话框
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
        }

        // 确认VIP升级
        function confirmVipUpgrade(targetLevel, config) {
            // 模拟API调用
            showLoading();
            
            setTimeout(() => {
                hideLoading();
                
                // 更新用户数据
                userData.balance -= config.cost;
                userData.vipLevel = targetLevel;
                userData.vipExpireTime = getVipExpireTime();

                // 更新页面显示
                updateVipDisplay();
                updateBalanceDisplay();

                // 隐藏确认对话框
                hideModal('vipUpgradeModal');

                // 显示成功提示
                showUpgradeSuccessModal(config.name);

                // 保存到本地存储
                localStorage.setItem('userData', JSON.stringify(userData));
            }, 1500);
        }

        // 显示升级成功对话框
        function showUpgradeSuccessModal(vipName) {
            const modal = document.getElementById('upgradeSuccessModal');
            const text = document.getElementById('upgradeSuccessText');
            
            text.textContent = `恭喜您成功升级到 ${vipName}！现在可以享受更多特权和更高收益。`;
            
            modal.classList.remove('hidden');
        }

        // 显示余额不足提示
        function showInsufficientBalanceModal(requiredAmount) {
            const shortfall = requiredAmount - userData.balance;
            showNotification(`余额不足！还需要 ${shortfall.toFixed(2)} USDT，请先充值。`, 'error');
        }

        // 更新VIP显示
        function updateVipDisplay() {
            // 更新当前VIP状态卡片
            const currentVipCard = document.querySelector('.vip-card h2');
            const vipNames = ['', 'VIP 1', 'VIP 2', 'VIP 3', 'VIP 4'];
            const vipSubtitles = ['', '青铜会员', '白银会员', '黄金会员', '钻石会员'];
            
            if (currentVipCard) {
                currentVipCard.textContent = vipNames[userData.vipLevel];
                currentVipCard.nextElementSibling.textContent = vipSubtitles[userData.vipLevel];
            }

            // 更新VIP等级对比区域的当前状态
            updateVipComparisonCards();
        }

        // 更新VIP等级对比卡片
        function updateVipComparisonCards() {
            const vipCards = document.querySelectorAll('[class*="border-2 border-indigo-500"], [class*="border border-gray-200"]');
            
            vipCards.forEach((card, index) => {
                const level = index + 1;
                const button = card.querySelector('button');
                
                if (level <= userData.vipLevel) {
                    // 已拥有的等级
                    card.className = card.className.replace('border-gray-200', 'border-green-500');
                    card.classList.add('bg-green-50');
                    button.textContent = '已拥有';
                    button.className = 'bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium cursor-default';
                    button.onclick = null;
                } else if (level === userData.vipLevel + 1) {
                    // 下一个可升级等级
                    card.className = card.className.replace('border-gray-200', 'border-indigo-500');
                    card.classList.add('bg-indigo-50');
                }
            });
        }

        // 更新余额显示
        function updateBalanceDisplay() {
            const balanceElements = document.querySelectorAll('#currentBalance');
            balanceElements.forEach(el => {
                el.textContent = `${userData.balance.toFixed(2)} USDT`;
            });
        }

        // 获取VIP过期时间
        function getVipExpireTime() {
            const now = new Date();
            const expireDate = new Date(now.setFullYear(now.getFullYear() + 1));
            return expireDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'error' ? 'bg-red-500' : type === 'warning' ? 'bg-yellow-500' : type === 'success' ? 'bg-green-500' : 'bg-blue-500';
            
            notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 ${bgColor} text-white px-6 py-3 rounded-xl shadow-lg z-50 transition-all duration-300`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.remove(), 3000);
        }

        // 显示/隐藏加载状态
        function showLoading() {
            // 可以添加加载动画
        }

        function hideLoading() {
            // 隐藏加载动画
        }

        // 隐藏模态框
        function hideModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.querySelector('.bg-white').classList.remove('scale-100');
            modal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        // 事件绑定
        document.addEventListener('DOMContentLoaded', function() {
            debug('页面加载完成，开始初始化');
            
            // 检查关键元素是否存在
            const modal = document.getElementById('vipUpgradeModal');
            const successModal = document.getElementById('upgradeSuccessModal');
            
            if (!modal) {
                debug('错误：找不到升级对话框');
            } else {
                debug('升级对话框找到');
            }
            
            if (!successModal) {
                debug('错误：找不到成功提示对话框');
            } else {
                debug('成功提示对话框找到');
            }
            
            // 取消升级按钮
            const cancelBtn = document.getElementById('cancelUpgrade');
            if (cancelBtn) {
                cancelBtn.onclick = () => hideModal('vipUpgradeModal');
                debug('取消按钮事件绑定成功');
            }
            
            // 关闭成功提示
            const closeBtn = document.getElementById('closeSuccessModal');
            if (closeBtn) {
                closeBtn.onclick = () => hideModal('upgradeSuccessModal');
                debug('关闭按钮事件绑定成功');
            }
            
            // 点击模态框背景关闭
            if (modal) {
                modal.onclick = (e) => {
                    if (e.target.id === 'vipUpgradeModal') hideModal('vipUpgradeModal');
                };
            }
            
            if (successModal) {
                successModal.onclick = (e) => {
                    if (e.target.id === 'upgradeSuccessModal') hideModal('upgradeSuccessModal');
                };
            }

            // 从本地存储加载用户数据
            const savedData = localStorage.getItem('userData');
            if (savedData) {
                userData = JSON.parse(savedData);
                updateVipDisplay();
                updateBalanceDisplay();
                debug('从本地存储加载用户数据');
            }
            
            debug('页面初始化完成');
            
            // 测试升级函数是否可用
            if (typeof upgradeVip === 'function') {
                debug('upgradeVip函数已定义');
            } else {
                debug('错误：upgradeVip函数未定义');
            }
        });
    </script>
</body>
</html>