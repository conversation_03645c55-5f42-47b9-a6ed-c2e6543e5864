<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息通知 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Tab样式 */
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 消息项动画 */
        .message-item {
            transition: all 0.3s ease;
        }

        .message-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 未读消息指示器 */
        .unread-dot {
            width: 8px;
            height: 8px;
            background: #ef4444;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-6 pb-8">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-xl font-bold">消息通知</h1>
                <button onclick="markAllRead()" class="text-white hover:text-gray-200 transition-colors text-sm">
                    全部已读
                </button>
            </div>
        </div>

        <!-- Tab切换 -->
        <div class="px-6 -mt-4 mb-6">
            <div class="bg-white rounded-2xl shadow-lg p-2 fade-in">
                <div class="flex">
                    <button onclick="switchTab('all')" id="tabAll" 
                        class="flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-active">
                        全部消息
                        <span class="ml-2 bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full" id="allCount">5</span>
                    </button>
                    <button onclick="switchTab('unread')" id="tabUnread" 
                        class="flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-inactive">
                        未读消息
                        <span class="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full" id="unreadCount">3</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 消息列表 -->
        <div class="px-6">
            <!-- 全部消息 -->
            <div id="allMessages" class="space-y-4">
                <!-- 系统通知 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden fade-in message-item" onclick="openMessage('system1')">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-bell text-blue-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-semibold text-gray-900">系统通知</h4>
                                    <div class="flex items-center">
                                        <div class="unread-dot mr-2"></div>
                                        <span class="text-xs text-gray-500">2分钟前</span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">您的账户安全等级已提升，新增双重验证功能</p>
                                <div class="flex items-center">
                                    <span class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">系统消息</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务通知 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden fade-in message-item" onclick="openMessage('task1')">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-tasks text-green-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-semibold text-gray-900">任务完成</h4>
                                    <div class="flex items-center">
                                        <div class="unread-dot mr-2"></div>
                                        <span class="text-xs text-gray-500">1小时前</span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">恭喜！您已完成"TikTok点赞任务"，获得奖励5.00 USDT</p>
                                <div class="flex items-center">
                                    <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">任务奖励</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提现通知 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden fade-in message-item" onclick="openMessage('withdraw1')">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-wallet text-purple-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-semibold text-gray-900">提现成功</h4>
                                    <div class="flex items-center">
                                        <div class="unread-dot mr-2"></div>
                                        <span class="text-xs text-gray-500">3小时前</span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">您的提现申请已处理完成，50.00 USDT已到账</p>
                                <div class="flex items-center">
                                    <span class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full">提现到账</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 活动通知 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden fade-in message-item" onclick="openMessage('activity1')">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-gift text-red-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-semibold text-gray-900">活动推荐</h4>
                                    <span class="text-xs text-gray-500">1天前</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">新春特惠活动开始！完成指定任务可获得双倍奖励</p>
                                <div class="flex items-center">
                                    <span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">限时活动</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全提醒 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden fade-in message-item" onclick="openMessage('security1')">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-shield-alt text-yellow-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h4 class="font-semibold text-gray-900">安全提醒</h4>
                                    <span class="text-xs text-gray-500">2天前</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">检测到您的账户在新设备上登录，请确认是否为本人操作</p>
                                <div class="flex items-center">
                                    <span class="text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">安全提醒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 未读消息 -->
            <div id="unreadMessages" class="space-y-4 hidden">
                <!-- 这里会显示未读消息 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-12 hidden">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-bell-slash text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无未读消息</h3>
                <p class="text-gray-500 text-sm">所有消息都已阅读完毕</p>
            </div>
        </div>
    </div>

    <!-- 消息详情弹窗 -->
    <div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-md w-full shadow-2xl transform transition-all modal-enter">
            <!-- 弹窗头部 -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-gray-900" id="modalTitle">消息详情</h3>
                <button onclick="closeMessageModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 消息内容 -->
            <div id="modalContent" class="mb-6">
                <!-- 内容将通过JavaScript动态填充 -->
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-3">
                <button onclick="closeMessageModal()" 
                    class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-colors">
                    关闭
                </button>
                <button onclick="markAsRead()" 
                    class="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    标记已读
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'all';
        let currentMessageId = null;
        let unreadCount = 3;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateUnreadMessages();
        });

        // 切换Tab
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新Tab样式
            document.getElementById('tabAll').className = tab === 'all' ? 
                'flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-active' :
                'flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-inactive';
            
            document.getElementById('tabUnread').className = tab === 'unread' ? 
                'flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-active' :
                'flex-1 py-3 px-4 rounded-xl text-sm font-semibold transition-all tab-inactive';
            
            // 显示对应内容
            if (tab === 'all') {
                document.getElementById('allMessages').classList.remove('hidden');
                document.getElementById('unreadMessages').classList.add('hidden');
                document.getElementById('emptyState').classList.add('hidden');
            } else {
                document.getElementById('allMessages').classList.add('hidden');
                if (unreadCount > 0) {
                    document.getElementById('unreadMessages').classList.remove('hidden');
                    document.getElementById('emptyState').classList.add('hidden');
                } else {
                    document.getElementById('unreadMessages').classList.add('hidden');
                    document.getElementById('emptyState').classList.remove('hidden');
                }
            }
        }

        // 更新未读消息
        function updateUnreadMessages() {
            const unreadMessages = document.getElementById('unreadMessages');
            const allMessages = document.getElementById('allMessages');
            
            // 复制未读消息到未读Tab
            const unreadItems = allMessages.querySelectorAll('.message-item');
            let unreadHTML = '';
            
            unreadItems.forEach((item, index) => {
                if (index < 3) { // 前3个消息为未读
                    unreadHTML += item.outerHTML;
                }
            });
            
            unreadMessages.innerHTML = unreadHTML;
        }

        // 打开消息详情
        function openMessage(messageId) {
            currentMessageId = messageId;
            const modal = document.getElementById('messageModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            
            // 根据消息ID设置内容
            const messages = {
                'system1': {
                    title: '系统通知',
                    content: `
                        <div class="text-center mb-4">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-bell text-blue-600 text-2xl"></i>
                            </div>
                            <div class="text-sm text-gray-500">2024-01-20 14:30</div>
                        </div>
                        <div class="text-gray-700 leading-relaxed">
                            <p class="mb-3">尊敬的用户，您好！</p>
                            <p class="mb-3">为了提升您的账户安全性，我们已为您的账户启用了双重验证功能。现在进行重要操作时，系统会要求您进行额外的身份验证。</p>
                            <p class="mb-3">新增功能包括：</p>
                            <ul class="list-disc list-inside mb-3 text-sm">
                                <li>提现时短信验证</li>
                                <li>异地登录提醒</li>
                                <li>密码修改验证</li>
                            </ul>
                            <p>如有疑问，请联系客服。</p>
                        </div>
                    `
                },
                'task1': {
                    title: '任务完成通知',
                    content: `
                        <div class="text-center mb-4">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-tasks text-green-600 text-2xl"></i>
                            </div>
                            <div class="text-sm text-gray-500">2024-01-20 13:15</div>
                        </div>
                        <div class="text-gray-700 leading-relaxed">
                            <p class="mb-3">🎉 恭喜您成功完成任务！</p>
                            <div class="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                                <div class="font-semibold text-green-800 mb-1">任务详情</div>
                                <div class="text-sm text-green-700">
                                    <p>任务名称：TikTok点赞任务</p>
                                    <p>完成时间：2024-01-20 13:15</p>
                                    <p>获得奖励：5.00 USDT</p>
                                </div>
                            </div>
                            <p>奖励已自动发放到您的账户余额中，请查收！</p>
                        </div>
                    `
                }
            };
            
            const messageData = messages[messageId] || {
                title: '消息详情',
                content: '<p class="text-gray-600">消息内容加载中...</p>'
            };
            
            title.textContent = messageData.title;
            content.innerHTML = messageData.content;
            
            // 显示弹窗
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭消息详情弹窗
        function closeMessageModal() {
            const modal = document.getElementById('messageModal');
            const modalContent = modal.querySelector('.bg-white');
            
            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 标记为已读
        function markAsRead() {
            if (currentMessageId) {
                // 移除未读标识
                const messageElements = document.querySelectorAll('.message-item');
                messageElements.forEach(element => {
                    if (element.getAttribute('onclick').includes(currentMessageId)) {
                        const unreadDot = element.querySelector('.unread-dot');
                        if (unreadDot) {
                            unreadDot.remove();
                            unreadCount--;
                            updateCounters();
                        }
                    }
                });
            }
            closeMessageModal();
        }

        // 全部标记为已读
        function markAllRead() {
            const unreadDots = document.querySelectorAll('.unread-dot');
            unreadDots.forEach(dot => dot.remove());
            unreadCount = 0;
            updateCounters();
            
            // 如果当前在未读Tab，切换到空状态
            if (currentTab === 'unread') {
                switchTab('unread');
            }
        }

        // 更新计数器
        function updateCounters() {
            document.getElementById('unreadCount').textContent = unreadCount;
            updateUnreadMessages();
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const messageModal = document.getElementById('messageModal');
            if (e.target === messageModal) {
                closeMessageModal();
            }
        });
    </script>
</body>
</html>
