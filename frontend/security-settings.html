<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全设置 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4f46e5;
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-6 pb-8">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-xl font-bold">安全设置</h1>
                <div class="w-6"></div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="px-6 py-6 -mt-4">
            <!-- 账户安全 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-shield-alt text-red-500 mr-2"></i>账户安全
                </h3>
                
                <div class="space-y-4">
                    <!-- 支付密码 -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer" onclick="changePaymentPassword()">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-lock text-green-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">支付密码</div>
                                <div class="text-sm text-gray-500">提现时需要输入的6位数字密码</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>





                    <!-- 邮箱验证 -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-envelope text-orange-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">邮箱验证</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                            </div>
                        </div>
                        <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">已验证</span>
                    </div>
                </div>
            </div>

            <!-- 安全选项 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-cog text-indigo-500 mr-2"></i>安全选项
                </h3>
                
                <div class="space-y-4">


                    <!-- 登录通知 -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-bell text-yellow-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">登录通知</div>
                                <div class="text-sm text-gray-500">异地登录时发送通知</div>
                            </div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked onchange="toggleLoginNotification(this)">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <!-- 自动登出 -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-clock text-gray-600"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">自动登出</div>
                                <div class="text-sm text-gray-500">30分钟无操作后自动登出</div>
                            </div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" onchange="toggleAutoLogout(this)">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>



            <!-- 危险操作 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>危险操作
                </h3>
                
                <div class="space-y-4">
                    <!-- 注销账户 -->
                    <button onclick="deleteAccount()" class="w-full p-4 bg-red-50 border border-red-200 rounded-xl text-red-600 hover:bg-red-100 transition-colors">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-user-times mr-2"></i>
                            <span class="font-medium">注销账户</span>
                        </div>
                        <div class="text-sm text-red-500 mt-1">此操作不可恢复，请谨慎操作</div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示弹窗 -->
    <div id="alertModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-info-circle text-blue-600 text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-xl font-bold text-gray-900 mb-2">提示</h3>
                <p id="alertMessage" class="text-gray-600 text-sm mb-6">功能开发中</p>
                
                <button onclick="closeAlertModal()" 
                    class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script>
        // 设置支付密码
        function changePaymentPassword() {
            window.location.href = 'payment-password.html';
        }





        // 切换登录通知
        function toggleLoginNotification(checkbox) {
            const status = checkbox.checked ? '已开启' : '已关闭';
            showAlert('设置已更新', `登录通知${status}`, 'success');
        }

        // 切换自动登出
        function toggleAutoLogout(checkbox) {
            const status = checkbox.checked ? '已开启' : '已关闭';
            showAlert('设置已更新', `自动登出${status}`, 'success');
        }



        // 注销账户
        function deleteAccount() {
            showAlert('危险操作', '账户注销功能需要客服协助，请联系客服处理', 'warning');
        }

        // 显示提示弹窗
        function showAlert(title, message, type = 'info') {
            const modal = document.getElementById('alertModal');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');
            
            titleEl.textContent = title;
            messageEl.textContent = message;
            
            if (type === 'success') {
                icon.className = 'w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-check-circle text-green-600 text-2xl"></i>';
            } else if (type === 'warning') {
                icon.className = 'w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>';
            } else if (type === 'info') {
                icon.className = 'w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-info-circle text-blue-600 text-2xl"></i>';
            }
            
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭提示弹窗
        function closeAlertModal() {
            const modal = document.getElementById('alertModal');
            const modalContent = modal.querySelector('.bg-white');
            
            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const alertModal = document.getElementById('alertModal');
            if (e.target === alertModal) {
                closeAlertModal();
            }
        });
    </script>
</body>
</html>
