<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 语言切换器样式 */
        .language-option:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }
        
        .language-option.active {
            background-color: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }
        
        #languageToggle .fa-chevron-down {
            transition: transform 0.2s ease;
        }
        
        #languageMenu {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        /* 多语言字体支持 */
        [lang="ko"], [lang="ko-KR"] {
            font-family: -apple-system, BlinkMacSystemFont, "Malgun Gothic", "맑은 고딕", serif;
        }
        
        [lang="en"], [lang="en-US"] {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        [lang="zh"], [lang="zh-CN"] {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", serif;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-50 to-violet-50 min-h-screen">

    <div class="flex flex-col justify-center min-h-screen px-6 py-12">
        <!-- Logo和标题 -->
        <div class="text-center mb-12">
            <div class="bg-gradient-to-r from-indigo-500 to-violet-500 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fab fa-tiktok text-white text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">欢迎回来</h1>
            <p class="text-gray-600">登录你的TikTok任务账户</p>
        </div>

        <!-- 登录表单 -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-6">
            <form class="space-y-6">
                <!-- 邮箱输入 -->
                <div>
                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-indigo-500"></i>邮箱地址
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email"
                                                placeholder="请输入邮箱地址"
                        class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                        value="<EMAIL>"
                    >
                </div>

                <!-- 密码输入 -->
                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-indigo-500"></i>密码
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                                                        placeholder="请输入密码"
                            class="w-full px-4 py-4 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                            value="123456"
                        >
                        <button type="button" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- 忘记密码 -->
                <div class="text-right">
                    <a href="#" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium" >
                        忘记密码？
                    </a>
                </div>

                <!-- 登录按钮 -->
                <button 
                    type="submit" 
                    class="w-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white font-semibold py-4 px-6 rounded-xl hover:from-indigo-600 hover:to-violet-600 transition-all duration-200 shadow-lg"
                    onclick="handleLogin(event)"
                >
                    <i class="fas fa-sign-in-alt mr-2"></i>立即登录
                </button>
            </form>
        </div>

        <!-- 注册链接 -->
        <div class="text-center">
            <p class="text-gray-600">
                还没有账户？
                <a href="register.html" class="text-indigo-600 hover:text-indigo-800 font-semibold ml-1" >
                    立即注册
                </a>
            </p>
        </div>

        <!-- 其他登录方式 -->
        <div class="mt-8">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-4 bg-gradient-to-br from-indigo-50 to-violet-50 text-gray-500">或者使用</span>
                </div>
            </div>

            <div class="mt-6 grid grid-cols-2 gap-4">
                <button class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
                    <i class="fab fa-google text-red-500 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">Google</span>
                </button>
                <button class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
                    <i class="fab fa-apple text-gray-800 mr-2"></i>
                    <span class="text-sm font-medium text-gray-700">Apple</span>
                </button>
            </div>
        </div>

        <!-- 底部说明 -->
        <div class="mt-8 text-center">
            <p class="text-xs text-gray-500">
                登录即表示您同意我们的
                <a href="#" class="text-indigo-600 hover:underline" >服务条款</a>
                和
                <a href="#" class="text-indigo-600 hover:underline" >隐私政策</a>
            </p>
        </div>
    </div>

    <!-- 自定义提示框 -->
    <div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                    <i id="alertIconClass" class="text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="alertMessage" class="text-gray-600 mb-6"></p>
                <button id="alertButton" class="w-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-indigo-600 hover:to-violet-600 transition-all">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->  
    <script>
        // 自定义提示框
        function showAlert(title, message, type = 'info') {
            
            const alertBox = document.getElementById('customAlert');
            const alertIcon = document.getElementById('alertIcon');
            const alertIconClass = document.getElementById('alertIconClass');
            const alertTitle = document.getElementById('alertTitle');
            const alertMessage = document.getElementById('alertMessage');
            const alertButton = document.getElementById('alertButton');
            
            // 设置图标和颜色
            switch(type) {
                case 'success':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-green-100';
                    alertIconClass.className = 'fas fa-check text-2xl text-green-600';
                    break;
                case 'error':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-red-100';
                    alertIconClass.className = 'fas fa-times text-2xl text-red-600';
                    break;
                case 'warning':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-yellow-100';
                    alertIconClass.className = 'fas fa-exclamation-triangle text-2xl text-yellow-600';
                    break;
                default:
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-blue-100';
                    alertIconClass.className = 'fas fa-info-circle text-2xl text-blue-600';
            }
            
            alertTitle.textContent = title;
            alertMessage.textContent = message;
            
            // 显示提示框
            alertBox.classList.remove('hidden');
            setTimeout(() => {
                alertBox.querySelector('.bg-white').classList.remove('scale-95');
                alertBox.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // 绑定关闭事件
            alertButton.onclick = () => hideAlert();
            alertBox.onclick = (e) => {
                if (e.target === alertBox) hideAlert();
            };
        }
        
        function hideAlert() {
            const alertBox = document.getElementById('customAlert');
            alertBox.querySelector('.bg-white').classList.remove('scale-100');
            alertBox.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                alertBox.classList.add('hidden');
            }, 300);
        }
        
        // 处理登录
        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // 简单的表单验证
            if (!email || !password) {
                showAlert('提示', '请填写完整的登录信息', 'warning');
                return;
            }
            
            // 模拟登录验证
            if (email === '<EMAIL>' && password === '123456') {
                // 模拟登录成功
                const userInfo = {
                    email: email,
                    nickname: '张小明',
                    vipLevel: 1,
                    balance: 128.56,
                    loginTime: new Date().toISOString()
                };
                
                // 保存用户信息到localStorage
                localStorage.setItem('userToken', 'demo_token_' + Date.now());
                localStorage.setItem('userInfo', JSON.stringify(userInfo));
                
                // 显示登录成功提示
                showAlert('登录成功', '欢迎回来！即将跳转到主页面', 'success');
                
                // 延迟跳转
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showAlert('登录失败', '邮箱或密码错误，请重试', 'error');
            }
        }
        
        // 切换密码显示
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载时检查是否已登录
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                window.location.href = 'index.html';
            }

            // 为密码显示切换按钮添加事件监听
            const eyeButton = document.querySelector('.fa-eye')?.parentElement;
            if (eyeButton) {
                eyeButton.addEventListener('click', function() {
                    const passwordInput = document.getElementById('password');
                    const eyeIcon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        eyeIcon.classList.remove('fa-eye');
                        eyeIcon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        eyeIcon.classList.remove('fa-eye-slash');
                        eyeIcon.classList.add('fa-eye');
                    }
                });
            }
        });
    </script>
</body>
</html>