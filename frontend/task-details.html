<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }

        /* 移动端容器 */
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        /* 确保所有内容都在容器内 */
        .mobile-container .px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }
        
        /* 防止内容溢出 */
        .mobile-container > * {
            max-width: 100%;
            box-sizing: border-box;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                width: 100%;
            }
        }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        /* 弹窗背景渐变 */
        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }



        .step-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .step-completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .step-pending {
            background: #d1d5db;
            color: #6b7280;
        }

        .upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #6366f1;
            background-color: #f8fafc;
        }

        .upload-area.dragover {
            border-color: #6366f1;
            background-color: #eef2ff;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .task-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                width: 100%;
            }
            
            .mobile-container .px-6 {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .mobile-container .px-6 {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }
        
        /* 文本截断和换行 */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        /* 确保容器不会溢出 */
        .mobile-container * {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* 修复flex布局中的文本溢出 */
        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>

<body>
    <!-- 移动端容器 -->
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-6 py-4">
            <div class="flex items-center justify-between text-white">
                <button onclick="goBackToSource()" class="p-2 -ml-2 hover:bg-white/20 rounded-full transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-lg font-semibold">任务详情</h1>
                <div class="flex items-center space-x-2">
                    <button class="p-2 hover:bg-white/20 rounded-full transition-colors">
                        <i class="fas fa-share-alt text-lg"></i>
                    </button>
                    <button class="p-2 hover:bg-white/20 rounded-full transition-colors">
                        <i class="fas fa-question-circle text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务信息卡片 -->
        <div class="px-6 -mt-2 relative z-10">
            <div class="task-card rounded-2xl p-6 text-white mb-6 shadow-lg fade-in">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-start min-w-0 flex-1 mr-4">
                        <div class="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fab fa-tiktok text-2xl"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h2 class="text-xl font-bold mb-1 truncate">TikTok点赞任务</h2>
                            <p class="opacity-90 text-sm line-clamp-2">为指定视频点赞获得奖励</p>
                            <div class="flex items-center mt-2 flex-wrap gap-2">
                                <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full flex-shrink-0">
                                    <i class="fas fa-crown mr-1"></i>VIP 1+
                                </span>
                                <span class="bg-white/20 text-white text-xs px-2 py-1 rounded-full flex-shrink-0">
                                    <i class="fas fa-clock mr-1"></i>5分钟
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right flex-shrink-0">
                        <p class="text-3xl font-bold">$2.50</p>
                        <p class="text-sm opacity-80">奖励金额</p>
                        <div class="mt-2 text-xs opacity-75">
                            <i class="fas fa-users mr-1"></i>已完成 1,234
                        </div>
                    </div>
                </div>

                <!-- 任务进度 -->
                <div class="bg-white/20 rounded-full p-1 mb-4">
                    <div class="bg-white/30 rounded-full h-3 relative overflow-hidden">
                        <div class="bg-gradient-to-r from-green-400 to-emerald-500 rounded-full h-3 transition-all duration-1000"
                            style="width: 33%" id="progressBar"></div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="opacity-90">进度：1/3 步骤已完成</span>
                    <span class="opacity-75">
                        <i class="fas fa-fire mr-1"></i>热门任务
                    </span>
                </div>
            </div>
        </div>

        <!-- TikTok视频预览 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-play-circle text-indigo-500 mr-2"></i>目标视频
                    </h3>
                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                        <i class="fas fa-heart mr-1"></i>必须点赞
                    </span>
                </div>

                <div class="flex space-x-4">
                    <!-- 视频预览 -->
                    <div class="relative bg-black rounded-xl overflow-hidden flex-shrink-0"
                        style="width: 120px; height: 160px;">
                        <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop"
                            alt="TikTok视频预览" class="w-full h-full object-cover">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <button
                                class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                                <i class="fas fa-play text-sm ml-0.5"></i>
                            </button>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="bg-black/50 text-white text-xs px-1.5 py-0.5 rounded">
                                <i class="fab fa-tiktok mr-1"></i>TikTok
                            </span>
                        </div>
                    </div>

                    <!-- 视频信息 -->
                    <div class="flex-1 min-w-0">
                        <div class="mb-3">
                            <h4 class="font-semibold text-gray-900 mb-1 truncate">@dance_queen_2024</h4>
                            <p class="text-gray-600 text-sm leading-relaxed line-clamp-3">超级有趣的舞蹈视频，跟着节拍一起摇摆！快来点赞支持 💃✨</p>
                        </div>

                        <div class="space-y-1 text-xs text-gray-500 mb-3">
                            <div class="flex items-center">
                                <i class="fas fa-eye w-4 mr-2 flex-shrink-0"></i>
                                <span>1.2M 观看</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-heart w-4 mr-2 text-red-500 flex-shrink-0"></i>
                                <span>85.6K 点赞</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-comment w-4 mr-2 flex-shrink-0"></i>
                                <span>3.2K 评论</span>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-1">
                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">#舞蹈</span>
                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">#热门</span>
                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">#推荐</span>
                        </div>
                    </div>
                </div>

                <!-- 视频链接 -->
                <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex items-center min-w-0 flex-1 mr-3">
                            <i class="fas fa-link text-gray-400 mr-2 flex-shrink-0"></i>
                            <div class="min-w-0 flex-1">
                                <p class="text-sm text-gray-600 font-mono truncate">tiktok.com/@dance_queen_2024/video/123456789</p>
                            </div>
                        </div>
                        <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors flex-shrink-0 ml-2">
                            <i class="fas fa-copy mr-1"></i>复制
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务步骤 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">
                    <i class="fas fa-list-ol text-indigo-500 mr-2"></i>任务步骤
                </h3>

                <!-- 步骤1 -->
                <div class="flex items-start mb-8">
                    <div class="step-completed w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm mr-4 mt-1 shadow-lg flex-shrink-0">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2 flex-wrap gap-2">
                            <h4 class="font-semibold text-gray-900">点击进入TikTok</h4>
                            <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full font-medium flex-shrink-0">
                                已完成
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">点击下方按钮跳转到TikTok应用或网页</p>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center min-w-0 flex-1 mr-2">
                                    <i class="fas fa-check-circle text-green-500 mr-2 flex-shrink-0"></i>
                                    <span class="text-green-700 text-sm font-medium">步骤已完成</span>
                                </div>
                                <span class="text-green-600 text-xs flex-shrink-0">
                                    <i class="fas fa-clock mr-1"></i>2分钟前
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤2 -->
                <div class="flex items-start mb-8">
                    <div class="step-number w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm mr-4 mt-1 shadow-lg pulse flex-shrink-0">
                        2
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2 flex-wrap gap-2">
                            <h4 class="font-semibold text-gray-900">为视频点赞</h4>
                            <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full font-medium flex-shrink-0">
                                进行中
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">在TikTok中找到指定视频并点击红心按钮点赞</p>

                        <!-- 操作提示 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mr-3 mt-0.5 flex-shrink-0"></i>
                                <div class="min-w-0 flex-1">
                                    <p class="text-blue-900 text-sm font-medium mb-1">操作提示</p>
                                    <ul class="text-blue-800 text-sm space-y-1">
                                        <li>• 确保已登录TikTok账号</li>
                                        <li>• 找到指定视频并点击红心按钮</li>
                                        <li>• 确认点赞成功（红心变红色）</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <button onclick="openTikTok()"
                            class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-3 rounded-xl font-semibold hover:from-red-600 hover:to-pink-600 transition-all shadow-lg w-full sm:w-auto">
                            <i class="fab fa-tiktok mr-2"></i>去TikTok点赞
                        </button>
                    </div>
                </div>

                <!-- 步骤3 -->
                <div class="flex items-start">
                    <div class="step-pending w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm mr-4 mt-1 flex-shrink-0">
                        3
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2 flex-wrap gap-2">
                            <h4 class="font-semibold text-gray-900">上传截图证明</h4>
                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium flex-shrink-0">
                                等待中
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">截图显示你已经点赞的页面并上传</p>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-yellow-500 mr-2 flex-shrink-0"></i>
                                <span class="text-yellow-700 text-sm font-medium">请先完成上一步骤</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 截图上传区域 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-camera text-indigo-500 mr-2"></i>上传截图
                    </h3>
                    <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                        步骤 3/3
                    </span>
                </div>

                <!-- 上传区域 -->
                <div class="upload-area rounded-xl p-8 text-center cursor-pointer mb-4"
                    onclick="document.getElementById('fileInput').click()">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400"></i>
                    </div>
                    <p class="text-gray-600 font-medium mb-2">点击上传截图或拖拽文件到此处</p>
                    <p class="text-gray-400 text-sm mb-3">支持 JPG、PNG 格式，最大 5MB</p>
                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                        <span><i class="fas fa-mobile-alt mr-1"></i>手机截图</span>
                        <span><i class="fas fa-desktop mr-1"></i>电脑截图</span>
                    </div>
                    <input type="file" id="fileInput" class="hidden" accept="image/*" onchange="handleFileUpload(this)">
                </div>

                <!-- 上传的图片预览区域 -->
                <div id="uploadedImages" class="hidden mb-4">
                    <h4 class="text-sm font-semibold text-gray-900 mb-2">已上传的截图</h4>
                    <div class="grid grid-cols-2 gap-3" id="imageGrid">
                        <!-- 动态生成的图片预览 -->
                    </div>
                </div>

                <!-- 截图要求 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mr-3 mt-0.5"></i>
                        <div>
                            <p class="text-blue-900 text-sm font-medium mb-2">截图要求</p>
                            <ul class="text-blue-800 text-sm space-y-1">
                                <li>• 截图必须显示点赞按钮为红色（已点赞状态）</li>
                                <li>• 确保视频标题和用户名清晰可见</li>
                                <li>• 截图不能有遮挡或模糊</li>
                                <li>• 建议上传2-3张不同角度的截图</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 示例截图 -->
                <div class="border-t border-gray-100 pt-4">
                    <div class="flex items-center justify-between mb-3">
                        <p class="text-sm font-semibold text-gray-700">
                            <i class="fas fa-eye text-green-500 mr-1"></i>正确示例
                        </p>
                        <button class="text-indigo-600 text-xs hover:text-indigo-800 transition-colors">
                            查看更多示例
                        </button>
                    </div>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1611162616305-c69b3fa7fbe0?w=120&h=160&fit=crop"
                                alt="截图示例1" class="w-20 h-24 object-cover rounded-lg border-2 border-green-200">
                            <div
                                class="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=120&h=160&fit=crop"
                                alt="截图示例2" class="w-20 h-24 object-cover rounded-lg border-2 border-green-200">
                            <div
                                class="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="flex-1 flex items-center">
                            <div class="text-xs text-gray-500">
                                <p class="font-medium mb-1">✅ 点赞按钮为红色</p>
                                <p class="font-medium mb-1">✅ 视频信息清晰</p>
                                <p class="font-medium">✅ 截图完整无遮挡</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务提交 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-paper-plane text-indigo-500 mr-2"></i>提交任务
                </h3>

                <!-- 提交检查清单 -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <p class="text-sm font-semibold text-gray-900 mb-3">
                        <i class="fas fa-clipboard-check text-indigo-500 mr-1"></i>提交前检查
                    </p>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-sm text-gray-700">已进入TikTok应用</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-circle text-gray-300 mr-2"></i>
                            <span class="text-sm text-gray-500">已为视频点赞</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-circle text-gray-300 mr-2"></i>
                            <span class="text-sm text-gray-500">已上传截图证明</span>
                        </div>
                    </div>
                </div>

                <!-- 奖励预览 -->
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-900 text-sm font-medium">任务完成后将获得</p>
                            <div class="flex items-center mt-1">
                                <span class="text-2xl font-bold text-green-600">$2.50</span>
                                <span class="text-green-600 text-sm ml-2">USDT</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-coins text-green-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮区域 -->
                <div class="space-y-3">
                    <button id="submitBtn"
                        class="w-full bg-gray-200 text-gray-500 font-semibold py-4 px-6 rounded-xl cursor-not-allowed transition-all">
                        <i class="fas fa-lock mr-2"></i>请先完成所有步骤
                    </button>

                    <!-- 激活状态的按钮（隐藏） -->
                    <button id="submitActiveBtn"
                        class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-4 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg hidden"
                        onclick="submitTask()">
                        <i class="fas fa-paper-plane mr-2"></i>提交任务（获得 $2.50）
                    </button>
                </div>

                <!-- 审核说明 -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        <i class="fas fa-clock mr-1"></i>提交后通常1-5分钟内完成审核
                    </p>
                </div>
            </div>
        </div>

        <!-- 帮助和支持 -->
        <div class="px-6 mb-6">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-headset text-blue-500 mr-3 mt-0.5"></i>
                    <div class="flex-1">
                        <p class="text-blue-900 text-sm font-medium mb-1">需要帮助？</p>
                        <p class="text-blue-800 text-sm mb-3">如遇到问题，我们的客服团队随时为您服务</p>
                        <div class="flex space-x-3">
                            <button
                                class="bg-blue-500 text-white text-xs px-3 py-1.5 rounded-full hover:bg-blue-600 transition-colors">
                                <i class="fas fa-comments mr-1"></i>在线客服
                            </button>
                            <button
                                class="bg-white text-blue-600 text-xs px-3 py-1.5 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors">
                                <i class="fas fa-question-circle mr-1"></i>常见问题
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关任务推荐 -->
        <div class="px-6 pb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-fire text-orange-500 mr-2"></i>推荐任务
                    </h3>
                    <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors">
                        查看更多
                    </button>
                </div>

                <div class="space-y-3">
                    <!-- 推荐任务1 -->
                    <div
                        class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user-plus text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 text-sm">TikTok关注任务</p>
                                <p class="text-gray-600 text-xs">关注指定用户</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">$5.00</p>
                            <p class="text-xs text-gray-500">5分钟</p>
                        </div>
                    </div>

                    <!-- 推荐任务2 -->
                    <div
                        class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-share text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 text-sm">TikTok分享任务</p>
                                <p class="text-gray-600 text-xs">分享视频到朋友圈</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">$3.50</p>
                            <p class="text-xs text-gray-500">3分钟</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交任务确认弹窗 -->
    <div id="submitModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <!-- 弹窗头部 -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-paper-plane text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">提交任务确认</h3>
                <p class="text-gray-600 text-sm">确认提交任务？提交后将进入审核阶段</p>
            </div>

            <!-- 任务信息 -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-900 text-sm font-medium">完成后将获得</p>
                        <div class="flex items-center mt-1">
                            <span class="text-xl font-bold text-green-600">$2.50</span>
                            <span class="text-green-600 text-sm ml-2">USDT</span>
                        </div>
                    </div>
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-green-600"></i>
                    </div>
                </div>
            </div>

            <!-- 按钮组 -->
            <div class="flex space-x-3">
                <button onclick="closeSubmitModal()"
                    class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-colors">
                    取消
                </button>
                <button onclick="confirmSubmitTask()"
                    class="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg">
                    确认提交
                </button>
            </div>
        </div>
    </div>

    <!-- 提交成功弹窗 -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <!-- 成功图标 -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">提交成功！</h3>
                <p class="text-gray-600 text-sm">任务已提交，审核通过后奖励将自动到账</p>
            </div>

            <!-- 奖励信息 -->
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-6">
                <div class="text-center">
                    <p class="text-green-900 text-sm font-medium mb-1">预计获得奖励</p>
                    <div class="flex items-center justify-center">
                        <span class="text-2xl font-bold text-green-600">$2.50</span>
                        <span class="text-green-600 text-sm ml-2">USDT</span>
                    </div>
                </div>
            </div>

            <!-- 审核说明 -->
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <i class="fas fa-info text-blue-600 text-xs"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-blue-900 text-sm font-medium mb-1">审核说明</p>
                        <p class="text-blue-700 text-xs">通常在1-5分钟内完成审核，请耐心等待</p>
                    </div>
                </div>
            </div>

            <!-- 确认按钮 -->
            <button onclick="closeSuccessModal()"
                class="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all shadow-lg">
                我知道了
            </button>
        </div>
    </div>

    <!-- TikTok跳转弹窗 -->
    <div id="tiktokModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <!-- 弹窗头部 -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-tiktok text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">跳转到TikTok</h3>
                <p class="text-gray-600 text-sm">即将为您打开TikTok应用</p>
            </div>

            <!-- 任务说明 -->
            <div class="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <i class="fas fa-heart text-red-600 text-sm"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-red-900 text-sm font-medium mb-1">任务要求</p>
                        <p class="text-red-700 text-xs" id="taskRequirement">请完成点赞后返回上传截图</p>
                    </div>
                </div>
            </div>

            <!-- 操作步骤 -->
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <i class="fas fa-list-ol text-blue-600 text-sm"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-blue-900 text-sm font-medium mb-2">操作步骤</p>
                        <ol class="text-blue-700 text-xs space-y-1">
                            <li>1. 在TikTok中找到指定内容</li>
                            <li id="actionStep">2. 点击红心按钮完成点赞</li>
                            <li>3. 截图保存操作证明</li>
                            <li>4. 返回此页面上传截图</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- 链接信息 -->
            <div class="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-link text-gray-600 text-sm"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-gray-900 text-sm font-medium mb-1">目标链接</p>
                        <p class="text-gray-600 text-xs break-all">tiktok.com/@dance_queen_2024</p>
                    </div>
                </div>
            </div>

            <!-- 按钮组 -->
            <div class="flex space-x-3">
                <button onclick="closeTikTokModal()"
                    class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-colors">
                    取消
                </button>
                <button onclick="confirmOpenTikTok()"
                    class="flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all shadow-lg">
                    <i class="fab fa-tiktok mr-2"></i>打开TikTok
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 页面加载时初始化任务数据
        document.addEventListener('DOMContentLoaded', function() {
            loadTaskData();
        });

        // 智能返回到来源页面
        function goBackToSource() {
            const sourcePage = localStorage.getItem('taskSourcePage');

            if (sourcePage) {
                // 根据来源页面返回到对应页面
                switch (sourcePage) {
                    case 'tasks':
                        window.location.href = 'index.html#tasks';
                        break;
                    case 'home':
                        console.log('返回首页'); // 调试信息
                        window.location.href = 'index.html#home';
                        break;
                    case 'profile':
                        console.log('返回个人中心'); // 调试信息
                        window.location.href = 'index.html#profile';
                        break;
                    default:
                        console.log('返回默认首页'); // 调试信息
                        window.location.href = 'index.html#home';
                }
            } else {
                console.log('没有来源信息，使用备用方案'); // 调试信息
                // 如果没有来源信息，尝试使用浏览器历史记录
                if (document.referrer && document.referrer.includes('index.html')) {
                    history.back();
                } else {
                    // 默认返回首页
                    window.location.href = 'index.html#home';
                }
            }
        }
        
        // 从localStorage加载任务数据
        function loadTaskData() {
            const taskData = localStorage.getItem('currentTask');
            if (taskData) {
                const task = JSON.parse(taskData);
                updateTaskDisplay(task);
            }
        }
        
        // 更新任务显示信息
        function updateTaskDisplay(task) {
            // 更新任务标题和描述
            const taskTitle = document.querySelector('h2.text-xl.font-bold');
            const taskDescription = document.querySelector('p.opacity-90.text-sm');
            const rewardAmount = document.querySelector('p.text-3xl.font-bold');
            const submitReward = document.querySelector('#submitActiveBtn');
            
            if (taskTitle) taskTitle.textContent = task.title;
            if (taskDescription) taskDescription.textContent = task.description;
            if (rewardAmount) rewardAmount.textContent = `$${task.reward}`;
            if (submitReward) {
                submitReward.innerHTML = `<i class="fas fa-paper-plane mr-2"></i>提交任务（获得 $${task.reward}）`;
            }

            // 更新奖励预览
            const rewardPreview = document.querySelector('.text-2xl.font-bold.text-green-600');
            if (rewardPreview) rewardPreview.textContent = `$${task.reward}`;

            // 更新弹窗中的奖励金额
            const modalRewards = document.querySelectorAll('#submitModal .text-xl.font-bold.text-green-600, #successModal .text-2xl.font-bold.text-green-600');
            modalRewards.forEach(element => {
                element.textContent = `$${task.reward}`;
            });

            // 根据任务类型更新图标和步骤
            updateTaskTypeSpecificContent(task.type);
        }
        
        // 根据任务类型更新特定内容
        function updateTaskTypeSpecificContent(taskType) {
            const actionButton = document.querySelector('button[onclick="openTikTok()"]');
            const stepTitle = document.querySelector('.flex.items-center.mb-2 h4');
            const stepDescription = document.querySelector('.text-gray-600.text-sm.mb-4');
            const requirementBadge = document.querySelector('.bg-red-100.text-red-600');
            
            switch(taskType) {
                case 'like':
                    if (actionButton) {
                        actionButton.innerHTML = '<i class="fab fa-tiktok mr-2"></i>去TikTok点赞';
                    }
                    if (stepTitle) stepTitle.textContent = '为视频点赞';
                    if (stepDescription) stepDescription.textContent = '在TikTok中找到指定视频并点击红心按钮点赞';
                    if (requirementBadge) {
                        requirementBadge.innerHTML = '<i class="fas fa-heart mr-1"></i>必须点赞';
                    }
                    break;
                    
                case 'follow':
                    if (actionButton) {
                        actionButton.innerHTML = '<i class="fab fa-tiktok mr-2"></i>去TikTok关注';
                    }
                    if (stepTitle) stepTitle.textContent = '关注用户';
                    if (stepDescription) stepDescription.textContent = '在TikTok中找到指定用户并点击关注按钮';
                    if (requirementBadge) {
                        requirementBadge.innerHTML = '<i class="fas fa-user-plus mr-1"></i>必须关注';
                    }
                    break;
                    
                case 'share':
                    if (actionButton) {
                        actionButton.innerHTML = '<i class="fab fa-tiktok mr-2"></i>去TikTok分享';
                    }
                    if (stepTitle) stepTitle.textContent = '分享视频';
                    if (stepDescription) stepDescription.textContent = '在TikTok中找到指定视频并分享到社交平台';
                    if (requirementBadge) {
                        requirementBadge.innerHTML = '<i class="fas fa-share mr-1"></i>必须分享';
                    }
                    break;
                    
                case 'comment':
                    if (actionButton) {
                        actionButton.innerHTML = '<i class="fab fa-tiktok mr-2"></i>去TikTok评论';
                    }
                    if (stepTitle) stepTitle.textContent = '发表评论';
                    if (stepDescription) stepDescription.textContent = '在TikTok中找到指定视频并发表正面评论';
                    if (requirementBadge) {
                        requirementBadge.innerHTML = '<i class="fas fa-comment mr-1"></i>必须评论';
                    }
                    break;
            }
        }
        
        // 打开TikTok - 显示确认弹窗
        function openTikTok() {
            const taskData = localStorage.getItem('currentTask');
            let taskType = 'like';
            if (taskData) {
                const task = JSON.parse(taskData);
                taskType = task.type;
            }

            // 更新弹窗内容根据任务类型
            updateTikTokModalContent(taskType);

            // 显示弹窗
            const modal = document.getElementById('tiktokModal');
            modal.classList.remove('hidden');
            // 触发动画
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 更新TikTok弹窗内容
        function updateTikTokModalContent(taskType) {
            const taskRequirement = document.getElementById('taskRequirement');
            const actionStep = document.getElementById('actionStep');

            let actionText = '';
            let icon = '';
            let stepText = '';

            switch(taskType) {
                case 'like':
                    actionText = '点赞';
                    icon = 'fas fa-heart';
                    stepText = '2. 点击红心按钮完成点赞';
                    break;
                case 'follow':
                    actionText = '关注';
                    icon = 'fas fa-user-plus';
                    stepText = '2. 点击关注按钮完成关注';
                    break;
                case 'share':
                    actionText = '分享';
                    icon = 'fas fa-share';
                    stepText = '2. 点击分享按钮完成分享';
                    break;
                case 'comment':
                    actionText = '评论';
                    icon = 'fas fa-comment';
                    stepText = '2. 发表正面评论';
                    break;
                default:
                    actionText = '完成任务';
                    icon = 'fas fa-heart';
                    stepText = '2. 完成指定操作';
            }

            if (taskRequirement) {
                taskRequirement.textContent = `请完成${actionText}后返回上传截图`;
            }
            if (actionStep) {
                actionStep.textContent = stepText;
            }

            // 更新图标
            const iconElement = document.querySelector('#tiktokModal .w-8.h-8.bg-red-100 i');
            if (iconElement) {
                iconElement.className = `${icon} text-red-600 text-sm`;
            }
        }

        // 关闭TikTok弹窗
        function closeTikTokModal() {
            const modal = document.getElementById('tiktokModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 确认打开TikTok
        function confirmOpenTikTok() {
            // 关闭弹窗
            closeTikTokModal();

            // 模拟打开TikTok应用
            // 在实际应用中，这里可以使用深度链接打开TikTok应用
            // window.location.href = 'tiktok://...';

            // 更新步骤状态
            setTimeout(() => {
                updateStepStatus(2, 'completed');
                updateProgressBar(66);
                enableUpload();
            }, 1000);
        }

        // 更新步骤状态
        function updateStepStatus(step, status) {
            const stepElement = document.querySelector(`.step-number:nth-of-type(${step})`);
            if (status === 'completed') {
                stepElement.className = 'step-completed w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm mr-4 mt-1 shadow-lg';
                stepElement.innerHTML = '<i class="fas fa-check"></i>';
            }
        }

        // 更新进度条
        function updateProgressBar(percentage) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
        }

        // 启用上传功能
        function enableUpload() {
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.classList.remove('cursor-not-allowed');
            uploadArea.classList.add('cursor-pointer');
        }

        // 处理文件上传
        function handleFileUpload(input) {
            const files = input.files;
            if (files.length > 0) {
                const uploadedImages = document.getElementById('uploadedImages');
                const imageGrid = document.getElementById('imageGrid');

                uploadedImages.classList.remove('hidden');

                for (let file of files) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'relative';
                        imageDiv.innerHTML = `
                            <img src="${e.target.result}" alt="上传的截图" class="w-full h-24 object-cover rounded-lg border-2 border-green-200">
                            <div class="absolute top-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        `;
                        imageGrid.appendChild(imageDiv);
                    };
                    reader.readAsDataURL(file);
                }

                // 启用提交按钮
                setTimeout(() => {
                    enableSubmit();
                    updateStepStatus(3, 'completed');
                    updateProgressBar(100);
                }, 1000);
            }
        }

        // 启用提交按钮
        function enableSubmit() {
            const submitBtn = document.getElementById('submitBtn');
            const submitActiveBtn = document.getElementById('submitActiveBtn');

            submitBtn.classList.add('hidden');
            submitActiveBtn.classList.remove('hidden');
        }

        // 提交任务 - 显示确认弹窗
        function submitTask() {
            const modal = document.getElementById('submitModal');
            modal.classList.remove('hidden');
            // 触发动画
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭提交确认弹窗
        function closeSubmitModal() {
            const modal = document.getElementById('submitModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 确认提交任务
        function confirmSubmitTask() {
            // 关闭确认弹窗
            closeSubmitModal();

            // 显示提交中状态
            const submitActiveBtn = document.getElementById('submitActiveBtn');
            submitActiveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
            submitActiveBtn.disabled = true;

            // 模拟提交过程
            setTimeout(() => {
                // 显示成功弹窗
                const successModal = document.getElementById('successModal');
                successModal.classList.remove('hidden');
                // 触发动画
                setTimeout(() => {
                    successModal.querySelector('.bg-white').classList.add('modal-enter');
                }, 10);

                // 恢复按钮状态
                submitActiveBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>提交任务（获得 $2.50）';
                submitActiveBtn.disabled = false;
            }, 2000);
        }

        // 关闭成功弹窗
        function closeSuccessModal() {
            const modal = document.getElementById('successModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
                // 可以跳转到任务列表或其他页面
                // window.location.href = 'tasks.html';
            }, 300);
        }

        // 点击弹窗外部关闭弹窗
        document.addEventListener('click', function(e) {
            const submitModal = document.getElementById('submitModal');
            const successModal = document.getElementById('successModal');
            const tiktokModal = document.getElementById('tiktokModal');

            if (e.target === submitModal) {
                closeSubmitModal();
            }
            if (e.target === successModal) {
                closeSuccessModal();
            }
            if (e.target === tiktokModal) {
                closeTikTokModal();
            }
        });

        // 拖拽上传功能
        const uploadArea = document.querySelector('.upload-area');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                handleFileUpload(document.getElementById('fileInput'));
            }
        });
    </script>
</body>

</html>