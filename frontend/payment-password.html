<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付密码设置 - TikTok任务平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .mobile-container {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .header {
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .header h1 {
            color: white;
            font-size: 20px;
            font-weight: 600;
        }

        .content {
            padding: 30px 20px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.inactive {
            background: #f3f4f6;
            color: #9ca3af;
        }

        .step.completed {
            background: #10b981;
            color: white;
        }

        .step-line {
            width: 40px;
            height: 2px;
            background: #e5e7eb;
            margin-top: 14px;
        }

        .step-line.completed {
            background: #10b981;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
            text-align: center;
        }

        .section-desc {
            color: #6b7280;
            font-size: 14px;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .password-input-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .password-digit {
            width: 50px;
            height: 50px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            background: #f9fafb;
            transition: all 0.3s ease;
        }

        .password-digit:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-digit.filled {
            border-color: #10b981;
            background: #ecfdf5;
            color: #059669;
        }

        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }

        .security-tips {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
        }

        .security-tips h4 {
            color: #92400e;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .security-tips ul {
            color: #92400e;
            font-size: 13px;
            line-height: 1.5;
            padding-left: 16px;
        }

        .success-animation {
            text-align: center;
            padding: 40px 20px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(0); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 480px) {
            .mobile-container {
                margin: 10px;
                border-radius: 16px;
            }
            
            .password-digit {
                width: 45px;
                height: 45px;
                font-size: 20px;
            }
            
            .password-input-container {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 头部 -->
        <div class="gradient-bg header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>支付密码设置</h1>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step-line" id="line1"></div>
                <div class="step inactive" id="step2">2</div>
                <div class="step-line" id="line2"></div>
                <div class="step inactive" id="step3">✓</div>
            </div>

            <!-- 第一步：设置密码 -->
            <div class="form-section" id="setPasswordSection">
                <div class="section-title">设置支付密码</div>
                <div class="section-desc">请设置6位数字密码，用于提现时的安全验证</div>
                
                <div class="password-input-container">
                    <input type="text" class="password-digit" maxlength="1" id="digit1" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="digit2" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="digit3" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="digit4" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="digit5" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="digit6" inputmode="numeric">
                </div>

                <button class="btn btn-primary" id="nextBtn" onclick="nextStep()" disabled>下一步</button>
            </div>

            <!-- 第二步：确认密码 -->
            <div class="form-section hidden" id="confirmPasswordSection">
                <div class="section-title">确认支付密码</div>
                <div class="section-desc">请再次输入刚才设置的6位数字密码</div>
                
                <div class="password-input-container">
                    <input type="text" class="password-digit" maxlength="1" id="confirm1" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="confirm2" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="confirm3" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="confirm4" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="confirm5" inputmode="numeric">
                    <input type="text" class="password-digit" maxlength="1" id="confirm6" inputmode="numeric">
                </div>

                <button class="btn btn-primary" id="confirmBtn" onclick="confirmPasswordAction()" disabled>确认设置</button>
                <button class="btn" style="background: #f3f4f6; color: #6b7280; margin-top: 10px;" onclick="goBackToStep1()">重新设置</button>
            </div>

            <!-- 第三步：设置成功 -->
            <div class="form-section hidden" id="successSection">
                <div class="success-animation">
                    <div class="success-icon">
                        <i class="fas fa-check" style="color: white; font-size: 32px;"></i>
                    </div>
                    <div class="section-title">设置成功！</div>
                    <div class="section-desc">您的支付密码已设置完成，提现时需要输入此密码进行验证</div>
                    <button class="btn btn-primary" onclick="goToSecuritySettings()">返回安全设置</button>
                </div>
            </div>

            <!-- 安全提示 -->
            <div class="security-tips" id="securityTips">
                <h4><i class="fas fa-shield-alt" style="margin-right: 8px;"></i>安全提示</h4>
                <ul>
                    <li>支付密码用于提现时的安全验证</li>
                    <li>请设置6位数字，避免使用生日等易猜测的数字</li>
                    <li>请妥善保管您的支付密码，不要告诉他人</li>
                    <li>如忘记密码，可通过邮箱验证重置</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let firstPassword = '';
        let confirmPassword = '';

        // 返回上一页
        function goBack() {
            if (currentStep === 1) {
                window.location.href = 'security-settings.html';
            } else {
                goBackToStep1();
            }
        }



        // 检查密码是否完整
        function checkPasswordComplete() {
            let password = '';
            for (let i = 1; i <= 6; i++) {
                const digit = document.getElementById(`digit${i}`).value;
                if (!digit) {
                    document.getElementById('nextBtn').disabled = true;
                    return;
                }
                password += digit;
            }
            firstPassword = password;
            document.getElementById('nextBtn').disabled = false;
        }

        // 下一步
        function nextStep() {
            currentStep = 2;
            document.getElementById('setPasswordSection').classList.add('hidden');
            document.getElementById('confirmPasswordSection').classList.remove('hidden');
            document.getElementById('securityTips').classList.add('hidden');
            
            // 更新步骤指示器
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step1').classList.add('completed');
            document.getElementById('line1').classList.add('completed');
            document.getElementById('step2').classList.remove('inactive');
            document.getElementById('step2').classList.add('active');
            
            // 聚焦第一个确认输入框
            document.getElementById('confirm1').focus();
        }



        // 检查确认密码是否完整
        function checkConfirmComplete() {
            let password = '';
            for (let i = 1; i <= 6; i++) {
                const digit = document.getElementById(`confirm${i}`).value;
                if (!digit) {
                    document.getElementById('confirmBtn').disabled = true;
                    return;
                }
                password += digit;
            }
            confirmPassword = password;
            document.getElementById('confirmBtn').disabled = false;
        }

        // 确认密码
        function confirmPasswordAction() {
            if (firstPassword !== confirmPassword) {
                alert('两次输入的密码不一致，请重新输入');
                // 清空确认密码输入框
                for (let i = 1; i <= 6; i++) {
                    const input = document.getElementById(`confirm${i}`);
                    input.value = '';
                    input.classList.remove('filled');
                }
                document.getElementById('confirm1').focus();
                document.getElementById('confirmBtn').disabled = true;
                return;
            }

            // 密码设置成功，保存状态
            localStorage.setItem('hasPaymentPassword', 'true');
            localStorage.setItem('paymentPassword', firstPassword); // 实际应用中应该加密存储

            currentStep = 3;
            document.getElementById('confirmPasswordSection').classList.add('hidden');
            document.getElementById('successSection').classList.remove('hidden');

            // 根据来源更新按钮文本
            const source = localStorage.getItem('paymentPasswordSource');
            const returnButton = document.querySelector('#successSection button');
            if (source === 'withdraw') {
                returnButton.innerHTML = '<i class="fas fa-arrow-left mr-2"></i>返回提现页面';
            } else {
                returnButton.innerHTML = '<i class="fas fa-arrow-left mr-2"></i>返回安全设置';
            }

            // 更新步骤指示器
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step2').classList.add('completed');
            document.getElementById('line2').classList.add('completed');
            document.getElementById('step3').classList.remove('inactive');
            document.getElementById('step3').classList.add('completed');
        }

        // 返回第一步
        function goBackToStep1() {
            currentStep = 1;
            document.getElementById('confirmPasswordSection').classList.add('hidden');
            document.getElementById('setPasswordSection').classList.remove('hidden');
            document.getElementById('securityTips').classList.remove('hidden');
            
            // 重置步骤指示器
            document.getElementById('step1').classList.add('active');
            document.getElementById('step1').classList.remove('completed');
            document.getElementById('line1').classList.remove('completed');
            document.getElementById('step2').classList.add('inactive');
            document.getElementById('step2').classList.remove('active');
            
            // 清空确认密码
            for (let i = 1; i <= 6; i++) {
                const input = document.getElementById(`confirm${i}`);
                input.value = '';
                input.classList.remove('filled');
            }
            document.getElementById('confirmBtn').disabled = true;
        }

        // 返回安全设置或来源页面
        function goToSecuritySettings() {
            // 检查来源页面
            const source = localStorage.getItem('paymentPasswordSource');

            if (source === 'withdraw') {
                // 如果来自提现页面，清除标记并返回提现页面
                localStorage.removeItem('paymentPasswordSource');
                window.location.replace('withdraw.html');
            } else {
                // 默认返回安全设置页面
                window.location.replace('security-settings.html');
            }
        }

        // 为每个输入框添加事件监听器
        function setupInputListeners() {
            console.log('Setting up input listeners...');

            // 设置密码输入框
            for (let i = 1; i <= 6; i++) {
                const input = document.getElementById(`digit${i}`);
                if (input) {
                    console.log(`Setting up listener for digit${i}`);
                    input.addEventListener('input', function(e) {
                        console.log(`Input event triggered on digit${i}`);
                        handleInput(this, i, 'digit');
                    });
                    input.addEventListener('keydown', function(e) {
                        handleKeydown(e, this, i, 'digit');
                    });
                } else {
                    console.log(`Input digit${i} not found!`);
                }
            }

            // 确认密码输入框
            for (let i = 1; i <= 6; i++) {
                const input = document.getElementById(`confirm${i}`);
                if (input) {
                    console.log(`Setting up listener for confirm${i}`);
                    input.addEventListener('input', function(e) {
                        console.log(`Input event triggered on confirm${i}`);
                        handleInput(this, i, 'confirm');
                    });
                    input.addEventListener('keydown', function(e) {
                        handleKeydown(e, this, i, 'confirm');
                    });
                } else {
                    console.log(`Input confirm${i} not found!`);
                }
            }
            console.log('Input listeners setup complete');
        }

        // 处理输入事件
        function handleInput(input, position, type) {
            console.log(`handleInput called: position=${position}, type=${type}, value=${input.value}`);

            // 只允许数字
            const value = input.value.replace(/[^0-9]/g, '');
            input.value = value;

            if (value) {
                input.classList.add('filled');
                console.log(`Value entered: ${value}, moving to next input`);

                // 自动跳转到下一个输入框
                if (position < 6) {
                    const nextInput = document.getElementById(`${type}${position + 1}`);
                    if (nextInput) {
                        console.log(`Focusing next input: ${type}${position + 1}`);
                        setTimeout(() => {
                            nextInput.focus();
                        }, 10);
                    } else {
                        console.log(`Next input not found: ${type}${position + 1}`);
                    }
                }
            } else {
                input.classList.remove('filled');
            }

            // 检查完整性
            if (type === 'digit') {
                checkPasswordComplete();
            } else {
                checkConfirmComplete();
            }
        }

        // 处理键盘事件
        function handleKeydown(e, input, position, type) {
            if (e.key === 'Backspace') {
                if (!input.value && position > 1) {
                    // 如果当前输入框为空，跳转到前一个输入框
                    const prevInput = document.getElementById(`${type}${position - 1}`);
                    if (prevInput) {
                        prevInput.focus();
                        prevInput.value = '';
                        prevInput.classList.remove('filled');
                        // 检查完整性
                        if (type === 'digit') {
                            checkPasswordComplete();
                        } else {
                            checkConfirmComplete();
                        }
                    }
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupInputListeners();
        });
    </script>
</body>
</html>
