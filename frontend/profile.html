<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .profile-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部用户信息 -->
    <div class="profile-gradient px-6 pt-6 pb-8">
        <div class="flex items-center justify-between text-white mb-6">
            <h1 class="text-xl font-bold">个人中心</h1>
            <button class="p-2 text-white/70 hover:text-white">
                <i class="fas fa-cog text-xl"></i>
            </button>
        </div>

        <div class="flex items-center">
            <div class="relative">
                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face" 
                     alt="用户头像" 
                     class="w-20 h-20 rounded-full border-4 border-white/30">
                <button class="absolute -bottom-1 -right-1 w-7 h-7 bg-white text-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </button>
            </div>
            <div class="ml-4 flex-1">
                <h2 class="text-2xl font-bold text-white">张小明</h2>
                <p class="text-white/80 text-sm mb-2">ID: 1234567</p>
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 px-3 py-1 rounded-full flex items-center mr-3">
                        <i class="fas fa-crown text-white text-xs mr-1"></i>
                        <span class="text-white text-sm font-medium">VIP 1</span>
                    </div>
                    <span class="text-white/80 text-sm">注册于 2024.01.15</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计数据 -->
    <div class="px-6 -mt-4 relative z-10">
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-indigo-600">156</p>
                    <p class="text-sm text-gray-500">完成任务</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">¥856.30</p>
                    <p class="text-sm text-gray-500">总收益</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-purple-600">24</p>
                    <p class="text-sm text-gray-500">推荐人数</p>
                </div>
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-bolt text-yellow-500 mr-2"></i>快捷功能
            </h3>
            <div class="grid grid-cols-4 gap-4">
                <button class="flex flex-col items-center p-3 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors" onclick="window.location.href='daily-checkin.html'">
                    <i class="fas fa-calendar-check text-blue-600 text-xl mb-2"></i>
                    <span class="text-sm font-medium text-blue-800">签到</span>
                </button>
                <button class="flex flex-col items-center p-3 bg-green-50 rounded-xl hover:bg-green-100 transition-colors" onclick="window.location.href='earnings-details.html'">
                    <i class="fas fa-gift text-green-600 text-xl mb-2"></i>
                    <span class="text-sm font-medium text-green-800">奖励</span>
                </button>
                <button class="flex flex-col items-center p-3 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors" onclick="window.location.href='referral.html'">
                    <i class="fas fa-users text-purple-600 text-xl mb-2"></i>
                    <span class="text-sm font-medium text-purple-800">邀请</span>
                </button>
                <button class="flex flex-col items-center p-3 bg-orange-50 rounded-xl hover:bg-orange-100 transition-colors" onclick="showCustomerService()">
                    <i class="fas fa-headset text-orange-600 text-xl mb-2"></i>
                    <span class="text-sm font-medium text-orange-800">客服</span>
                </button>
            </div>
        </div>

        <!-- 菜单列表 -->
        <div class="bg-white rounded-2xl shadow-lg mb-6">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-user-circle text-indigo-500 mr-2"></i>账户管理
                </h3>
            </div>
            <div class="divide-y divide-gray-100">
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-edit text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">编辑资料</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-shield-alt text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">安全设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-key text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">修改密码</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-bell text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">消息通知</span>
                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">3</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg mb-6">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-chart-line text-green-500 mr-2"></i>数据统计
                </h3>
            </div>
            <div class="divide-y divide-gray-100">
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='task-records.html'">
                    <div class="flex items-center">
                        <i class="fas fa-tasks text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">任务记录</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='earnings-details.html'">
                    <div class="flex items-center">
                        <i class="fas fa-coins text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">收益明细</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='referral-details.html'">
                    <div class="flex items-center">
                        <i class="fas fa-users text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">推荐明细</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg mb-6">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-life-ring text-blue-500 mr-2"></i>帮助中心
                </h3>
            </div>
            <div class="divide-y divide-gray-100">
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-question-circle text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">常见问题</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-book text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">新手教程</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-comments text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">意见反馈</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
                <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <i class="fas fa-file-contract text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">用户协议</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </button>
            </div>
        </div>

        <!-- 每日任务进度 -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-trophy text-yellow-500 mr-2"></i>今日任务进度
                </h3>
                <span class="text-sm text-gray-500">12/15 完成</span>
            </div>
            
            <div class="bg-gray-200 rounded-full h-3 mb-4">
                <div class="bg-gradient-to-r from-green-400 to-emerald-500 h-3 rounded-full" style="width: 80%"></div>
            </div>
            
            <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600">还需完成 3 个任务</span>
                <span class="text-green-600 font-medium">获得额外奖励 ¥5.00</span>
            </div>
        </div>

        <!-- 设置选项 -->
        <div class="bg-white rounded-2xl shadow-lg mb-6">
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-cogs text-gray-500 mr-2"></i>应用设置
                </h3>
            </div>
            <div class="divide-y divide-gray-100">
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <i class="fas fa-moon text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">深色模式</span>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                </div>
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <i class="fas fa-language text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">语言设置</span>
                    </div>
                    <div class="flex items-center text-gray-500">
                        <span class="text-sm mr-2">简体中文</span>
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <i class="fas fa-download text-gray-400 w-5 mr-3"></i>
                        <span class="text-gray-900">检查更新</span>
                    </div>
                    <div class="flex items-center text-gray-500">
                        <span class="text-sm mr-2">v2.1.0</span>
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="bg-white rounded-2xl shadow-lg">
            <button class="w-full flex items-center justify-center p-4 text-red-600 hover:bg-red-50 transition-colors rounded-2xl" onclick="logout()">
                <i class="fas fa-sign-out-alt mr-2"></i>
                <span class="font-medium">退出登录</span>
            </button>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs font-medium">钱包</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs font-medium">VIP</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">推荐</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-indigo-600">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="customConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-red-100">
                    <i class="fas fa-sign-out-alt text-2xl text-red-600"></i>
                </div>
                <h3 id="confirmTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="confirmMessage" class="text-gray-600 mb-6"></p>
                <div class="flex space-x-3">
                    <button id="cancelButton" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                        取消
                    </button>
                    <button id="confirmButton" class="flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 自定义确认对话框
        function showConfirm(title, message, onConfirm) {
            const confirmBox = document.getElementById('customConfirm');
            const confirmTitle = document.getElementById('confirmTitle');
            const confirmMessage = document.getElementById('confirmMessage');
            const confirmButton = document.getElementById('confirmButton');
            const cancelButton = document.getElementById('cancelButton');
            
            confirmTitle.textContent = title;
            confirmMessage.textContent = message;
            
            // 显示确认框
            confirmBox.classList.remove('hidden');
            setTimeout(() => {
                confirmBox.querySelector('.bg-white').classList.remove('scale-95');
                confirmBox.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // 绑定事件
            confirmButton.onclick = () => {
                hideConfirm();
                onConfirm();
            };
            
            cancelButton.onclick = () => hideConfirm();
            confirmBox.onclick = (e) => {
                if (e.target === confirmBox) hideConfirm();
            };
        }
        
        function hideConfirm() {
            const confirmBox = document.getElementById('customConfirm');
            confirmBox.querySelector('.bg-white').classList.remove('scale-100');
            confirmBox.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                confirmBox.classList.add('hidden');
            }, 300);
        }
        
        // 退出登录功能
        function logout() {
            showConfirm('退出登录', '确定要退出登录吗？', () => {
                // 清除本地存储的用户数据
                localStorage.removeItem('userToken');
                localStorage.removeItem('userInfo');
                localStorage.removeItem('currentTask');

                // 跳转到登录页面
                window.location.href = 'login.html';
            });
        }

        // 客服功能
        function showCustomerService() {
            showConfirm('联系客服', '请选择联系方式：\n\n📧 邮箱：<EMAIL>\n💬 微信：TikTokTask2024\n📱 QQ群：123456789\n\n工作时间：9:00-21:00', () => {
                // 可以添加复制联系方式到剪贴板的功能
                navigator.clipboard.writeText('<EMAIL>').then(() => {
                    alert('客服邮箱已复制到剪贴板');
                }).catch(() => {
                    alert('请手动复制客服邮箱：<EMAIL>');
                });
            });
        }
    </script>
</body>
</html>