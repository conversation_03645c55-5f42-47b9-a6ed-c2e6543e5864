<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日签到 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .checkin-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .reward-shine {
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .day-card.checked {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .day-card.today {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
            animation: pulse 2s infinite;
        }
        .day-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        .day-card:hover {
            transform: translateY(-2px);
        }
        .icon-container {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 auto 4px auto;
            display: grid;
            place-items: center;
        }
        .vip-points-card {
            background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
        }
        .task-card-hover {
            transition: all 0.3s ease;
        }
        .task-card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="checkin-gradient shadow-lg">
        <div class="flex items-center justify-between p-4 text-white">
            <button onclick="window.location.href='index.html'" class="p-2 -ml-2 text-white/70 hover:text-white">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-xl font-bold">每日签到</h1>
            <button class="p-2 -mr-2 text-white/70 hover:text-white">
                <i class="fas fa-history text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 签到奖励展示 -->
    <div class="p-6">
        <div class="checkin-gradient rounded-2xl p-6 text-white mb-6 shadow-xl relative overflow-hidden">
            <div class="reward-shine absolute inset-0 w-full h-full"></div>
            <div class="relative text-center">
                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-check text-3xl"></i>
                </div>
                <h2 class="text-2xl font-bold mb-2">连续签到第 4 天</h2>
                <p class="opacity-90 mb-6">坚持签到，获得更多奖励</p>
                
                <!-- 立即签到按钮 -->
                <button class="w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-xl p-4 transition-all duration-300 border border-white/30 hover:border-white/50">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-crown text-yellow-300 text-2xl mr-3"></i>
                        <div class="text-center">
                            <p class="text-lg font-bold text-white">立即签到</p>
                            <p class="text-sm opacity-90 text-white">获得今日VIP积分奖励</p>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- VIP积分余额 -->
        <div class="vip-points-card rounded-2xl p-6 text-white mb-6 shadow-xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-white/80 text-sm mb-1">我的VIP积分</p>
                    <div class="flex items-center">
                        <i class="fas fa-crown text-yellow-300 text-2xl mr-3"></i>
                        <span class="text-3xl font-bold">168</span>
                        <span class="text-lg ml-2 opacity-90">积分</span>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-white/80 text-sm mb-1">当前等级</p>
                    <div class="bg-white/20 rounded-lg px-3 py-1">
                        <span class="font-semibold">VIP 1</span>
                    </div>
                    <p class="text-xs text-white/70 mt-1">还需132积分升级VIP2</p>
                </div>
            </div>
        </div>



        <!-- VIP积分任务 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-tasks text-purple-500 mr-2"></i>VIP积分任务
            </h3>
            
            <div class="space-y-4">
                <!-- 任务1: 完成任务获得积分 -->
                <div class="task-card-hover flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-play-circle text-white text-lg"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">完成TikTok任务</p>
                            <p class="text-sm text-gray-600">每完成1个任务可获得积分</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-blue-600">+3</p>
                        <p class="text-xs text-gray-500">VIP积分</p>
                    </div>
                </div>

                <!-- 任务2: 邀请好友获得积分 -->
                <div class="task-card-hover flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user-plus text-white text-lg"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">邀请好友注册</p>
                            <p class="text-sm text-gray-600">每邀请1个好友注册可获得积分</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-green-600">+3</p>
                        <p class="text-xs text-gray-500">VIP积分</p>
                    </div>
                </div>

                <!-- 任务3: 每日活跃 -->
                <div class="task-card-hover flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl border border-orange-100 cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-fire text-white text-lg"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">每日活跃奖励</p>
                            <p class="text-sm text-gray-600">在线时长超过30分钟可获得积分</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-orange-600">+2</p>
                        <p class="text-xs text-gray-500">VIP积分</p>
                    </div>
                </div>

                <!-- 任务4: 分享平台 -->
                <div class="task-card-hover flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl border border-pink-100 cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-share-alt text-white text-lg"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">分享平台给好友</p>
                            <p class="text-sm text-gray-600">每分享1次可获得积分</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-pink-600">+1</p>
                        <p class="text-xs text-gray-500">VIP积分</p>
                    </div>
                </div>
            </div>

            <!-- VIP积分说明 -->
            <div class="mt-6 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl border border-purple-100">
                <div class="flex items-start">
                    <i class="fas fa-crown text-purple-500 mr-3 mt-1"></i>
                    <div>
                        <p class="font-medium text-purple-900 mb-2">VIP积分用途</p>
                        <ul class="text-sm text-purple-800 space-y-1">
                            <li>• 100积分可升级VIP1等级</li>
                            <li>• 300积分可升级VIP2等级</li>
                            <li>• 800积分可升级VIP3等级</li>
                            <li>• 2000积分可升级VIP4等级</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 签到规则 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>签到规则
            </h3>
            
            <div class="space-y-3">
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <p class="text-gray-700 text-sm">每日签到可获得 VIP积分 奖励，连续签到奖励递增</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <p class="text-gray-700 text-sm">周日签到可获得额外大奖，最高 30 VIP积分</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <p class="text-gray-700 text-sm">中断签到后重新开始计算，保持连续很重要</p>
                </div>
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <p class="text-gray-700 text-sm">VIP积分可用于升级VIP等级，享受更多特权</p>
                </div>
            </div>
        </div>

        <!-- 签到记录 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-history text-purple-500 mr-2"></i>最近签到
                </h3>
                <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                    查看全部
                </button>
            </div>

            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">2024-07-27</p>
                            <p class="text-sm text-gray-500">连续签到第3天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+15 VIP积分</p>
                        <p class="text-xs text-gray-500">已发放</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">2024-07-26</p>
                            <p class="text-sm text-gray-500">连续签到第2天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+12 VIP积分</p>
                        <p class="text-xs text-gray-500">已发放</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">2024-07-25</p>
                            <p class="text-sm text-gray-500">连续签到第1天</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+10 VIP积分</p>
                        <p class="text-xs text-gray-500">已发放</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动提示 -->
    <div class="fixed bottom-24 left-6 right-6 bg-purple-50 border border-purple-200 rounded-xl p-4 shadow-lg">
        <div class="flex items-center">
            <i class="fas fa-crown text-purple-500 mr-3"></i>
            <div>
                <p class="text-purple-900 font-medium text-sm">VIP积分提示</p>
                <p class="text-purple-800 text-xs">完成任务和邀请好友可快速获得VIP积分升级</p>
            </div>
        </div>
    </div>
</body>
</html>