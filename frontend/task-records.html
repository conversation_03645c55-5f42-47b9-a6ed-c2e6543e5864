<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务记录 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .task-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .status-pending {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .status-rejected {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .filter-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="task-gradient shadow-lg">
        <div class="flex items-center justify-between p-4 text-white">
            <button onclick="window.location.href='index.html#profile'" class="p-2 -ml-2 text-white/70 hover:text-white">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-xl font-bold">任务记录</h1>
            <button class="p-2 -mr-2 text-white/70 hover:text-white">
                <i class="fas fa-filter text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 统计概览 -->
    <div class="p-6">
        <div class="task-gradient rounded-2xl p-6 text-white mb-6 shadow-xl">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold mb-2">任务统计</h2>
                <p class="opacity-90">您的任务完成情况</p>
            </div>
            
            <div class="grid grid-cols-3 gap-4">
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold">156</p>
                    <p class="text-sm opacity-90">总任务</p>
                </div>
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold">142</p>
                    <p class="text-sm opacity-90">已完成</p>
                </div>
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold">91%</p>
                    <p class="text-sm opacity-90">成功率</p>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">筛选条件</h3>
                <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    重置
                </button>
            </div>
            
            <!-- 状态筛选 -->
            <div class="flex flex-wrap gap-2 mb-4">
                <button class="filter-active px-4 py-2 rounded-full text-sm font-medium transition-all">
                    全部
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    已完成
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    审核中
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    已拒绝
                </button>
            </div>

            <!-- 任务类型筛选 -->
            <div class="flex flex-wrap gap-2">
                <button class="px-3 py-1 rounded-lg text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200 transition-all">
                    <i class="fas fa-heart mr-1"></i>点赞
                </button>
                <button class="px-3 py-1 rounded-lg text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 transition-all">
                    <i class="fas fa-user-plus mr-1"></i>关注
                </button>
                <button class="px-3 py-1 rounded-lg text-xs font-medium bg-green-100 text-green-700 hover:bg-green-200 transition-all">
                    <i class="fas fa-comment mr-1"></i>评论
                </button>
                <button class="px-3 py-1 rounded-lg text-xs font-medium bg-purple-100 text-purple-700 hover:bg-purple-200 transition-all">
                    <i class="fas fa-share mr-1"></i>分享
                </button>
            </div>
        </div>

        <!-- 任务记录列表 -->
        <div class="space-y-4">
            <!-- 已完成任务 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start">
                        <div class="w-12 h-12 status-completed rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-heart text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">TikTok点赞任务</h4>
                            <p class="text-sm text-gray-600 mb-2">为指定视频点赞并截图上传</p>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                <span>2024-01-15 14:30</span>
                                <span class="mx-2">•</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>任务ID: #TK001</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-2">
                            <i class="fas fa-check-circle mr-1"></i>已完成
                        </span>
                        <p class="text-lg font-bold text-green-600">+2.50 USDT</p>
                    </div>
                </div>
                
                <!-- 任务详情 -->
                <div class="bg-gray-50 rounded-xl p-3 mb-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">提交时间:</span>
                        <span class="font-medium">2024-01-15 14:25</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-gray-600">审核时间:</span>
                        <span class="font-medium">2024-01-15 14:32</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-gray-600">完成用时:</span>
                        <span class="font-medium">2分钟</span>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button class="flex-1 bg-indigo-50 text-indigo-600 py-2 px-4 rounded-xl font-medium hover:bg-indigo-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>查看详情
                    </button>
                    <button class="flex-1 bg-gray-50 text-gray-600 py-2 px-4 rounded-xl font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-redo mr-2"></i>再做一次
                    </button>
                </div>
            </div>

            <!-- 审核中任务 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start">
                        <div class="w-12 h-12 status-pending rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-user-plus text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">TikTok关注任务</h4>
                            <p class="text-sm text-gray-600 mb-2">关注指定用户并截图证明</p>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                <span>2024-01-15 16:45</span>
                                <span class="mx-2">•</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>任务ID: #TK002</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mb-2">
                            <i class="fas fa-clock mr-1"></i>审核中
                        </span>
                        <p class="text-lg font-bold text-yellow-600">+2.00 USDT</p>
                    </div>
                </div>
                
                <!-- 审核进度 -->
                <div class="bg-yellow-50 rounded-xl p-3 mb-3">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-yellow-800">审核进度</span>
                        <span class="text-sm text-yellow-600">预计2小时内完成</span>
                    </div>
                    <div class="w-full bg-yellow-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full w-3/4"></div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button class="flex-1 bg-yellow-50 text-yellow-600 py-2 px-4 rounded-xl font-medium hover:bg-yellow-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>查看提交
                    </button>
                    <button class="flex-1 bg-gray-50 text-gray-600 py-2 px-4 rounded-xl font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-question-circle mr-2"></i>联系客服
                    </button>
                </div>
            </div>
            <!-- 被拒绝任务 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-start">
                        <div class="w-12 h-12 status-rejected rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-comment text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 mb-1">TikTok评论任务</h4>
                            <p class="text-sm text-gray-600 mb-2">为视频发表评论并截图</p>
                            <div class="flex items-center text-xs text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                <span>2024-01-15 12:20</span>
                                <span class="mx-2">•</span>
                                <i class="fas fa-eye mr-1"></i>
                                <span>任务ID: #TK003</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mb-2">
                            <i class="fas fa-times-circle mr-1"></i>已拒绝
                        </span>
                        <p class="text-lg font-bold text-gray-400 line-through">+3.00 USDT</p>
                    </div>
                </div>

                <!-- 拒绝原因 -->
                <div class="bg-red-50 rounded-xl p-3 mb-3">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-red-500 mt-1 mr-2"></i>
                        <div>
                            <p class="text-sm font-medium text-red-800 mb-1">拒绝原因</p>
                            <p class="text-sm text-red-700">截图不清晰，无法确认评论内容，请重新提交高清截图</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button class="flex-1 bg-red-50 text-red-600 py-2 px-4 rounded-xl font-medium hover:bg-red-100 transition-colors">
                        <i class="fas fa-redo mr-2"></i>重新提交
                    </button>
                    <button class="flex-1 bg-gray-50 text-gray-600 py-2 px-4 rounded-xl font-medium hover:bg-gray-100 transition-colors">
                        <i class="fas fa-question-circle mr-2"></i>申诉
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="text-center mt-6">
            <button class="bg-white text-gray-600 px-6 py-3 rounded-xl shadow-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-down mr-2"></i>加载更多
            </button>
        </div>
    </div>
</body>
</html>
