<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USDT提现 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .usdt-icon {
            background: linear-gradient(135deg, #26A17B 0%, #22C55E 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-6 py-4">
            <div class="flex items-center justify-between text-white">
                <button onclick="goBackToWallet()" class="p-2 -ml-2 hover:bg-white/20 rounded-full transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-lg font-semibold">USDT提现</h1>
                <button class="p-2 hover:bg-white/20 rounded-full transition-colors">
                    <i class="fas fa-history text-lg"></i>
                </button>
            </div>
        </div>
        
        <!-- 可用余额显示 -->
        <div class="px-6 -mt-2 relative z-10">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">可提现余额</p>
                        <div class="flex items-center">
                            <div class="usdt-icon w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">₮</span>
                            </div>
                            <span class="text-2xl font-bold text-gray-900">128.56</span>
                            <span class="text-lg ml-2 text-gray-600">USDT</span>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-gray-600 text-sm mb-1">冻结金额</p>
                        <p class="text-lg font-semibold text-orange-600">28.00 USDT</p>
                    </div>
                </div>
                
                <!-- 提现限制提示 -->
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <div class="text-sm text-blue-800">
                            <p class="font-medium">提现说明</p>
                            <p>最小提现：10 USDT • 每日限额：5,000 USDT • 手续费：2 USDT</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提现金额输入 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-money-bill-wave text-green-500 mr-2"></i>提现金额
                </h3>
                
                <!-- 金额输入框 -->
                <div class="mb-4">
                    <div class="relative">
                        <input type="number" id="withdrawAmount" placeholder="请输入提现金额" 
                               class="w-full px-4 py-4 pr-16 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-xl font-bold text-center"
                               oninput="calculateFee()">
                        <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">USDT</span>
                    </div>
                </div>
                
                <!-- 快捷金额按钮 -->
                <div class="grid grid-cols-4 gap-2 mb-4">
                    <button class="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200" onclick="setAmount(50)">
                        50
                    </button>
                    <button class="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200" onclick="setAmount(100)">
                        100
                    </button>
                    <button class="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200" onclick="setAmount(200)">
                        200
                    </button>
                    <button class="bg-blue-100 text-blue-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-200" onclick="setAmount(128.56)">
                        全部
                    </button>
                </div>
                
                <!-- 费用计算 -->
                <div id="feeCalculation" class="bg-gray-50 rounded-lg p-4 hidden">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">提现金额</span>
                            <span class="font-semibold" id="withdrawAmountDisplay">0 USDT</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">手续费</span>
                            <span class="font-semibold text-orange-600" id="feeDisplay">2 USDT</span>
                        </div>
                        <div class="border-t border-gray-200 pt-2 flex justify-between">
                            <span class="text-gray-900 font-semibold">实际到账</span>
                            <span class="font-bold text-green-600" id="actualAmountDisplay">0 USDT</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提现地址 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-wallet text-purple-500 mr-2"></i>提现地址
                    </h3>
                    <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors" onclick="showAddressBook()">
                        <i class="fas fa-address-book mr-1"></i>地址簿
                    </button>
                </div>
                
                <!-- 网络选择 -->
                <div class="mb-4">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">选择网络</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="network-option border-2 border-indigo-200 bg-indigo-50 rounded-lg p-3 cursor-pointer" onclick="selectNetwork('trc20')">
                            <div class="flex items-center">
                                <div class="w-4 h-4 border-2 border-indigo-500 rounded-full flex items-center justify-center mr-3">
                                    <div class="w-2 h-2 bg-indigo-500 rounded-full"></div>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">TRC20</p>
                                    <p class="text-xs text-gray-600">手续费：2 USDT</p>
                                </div>
                            </div>
                        </div>
                        <div class="network-option border-2 border-gray-200 bg-gray-50 rounded-lg p-3 cursor-pointer" onclick="selectNetwork('erc20')">
                            <div class="flex items-center">
                                <div class="w-4 h-4 border-2 border-gray-300 rounded-full mr-3"></div>
                                <div>
                                    <p class="font-semibold text-gray-900">ERC20</p>
                                    <p class="text-xs text-gray-600">手续费：15 USDT</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 地址输入 -->
                <div class="mb-4">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">提现地址</label>
                    <div class="relative">
                        <input type="text" id="withdrawAddress" placeholder="请输入或粘贴USDT地址" 
                               class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm">
                        <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="pasteAddress()">
                            <i class="fas fa-paste"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">请确保地址正确，转账无法撤销</p>
                </div>
                
                <!-- 地址验证 -->
                <div id="addressValidation" class="hidden">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-green-700 text-sm font-medium">地址格式正确</span>
                        </div>
                    </div>
                </div>

                <!-- 常用地址快速选择 -->
                <div id="quickAddresses" class="mt-4 hidden">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-gray-700">常用地址</span>
                        <button onclick="showAddressBook()" class="text-xs text-indigo-600 hover:text-indigo-800">
                            查看全部 <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                    <div id="quickAddressList" class="space-y-2">
                        <!-- 快速地址项将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全验证 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-shield-alt text-red-500 mr-2"></i>安全验证
                </h3>
                
                <!-- 支付密码 -->
                <div class="mb-4">
                    <label class="block text-sm font-semibold text-gray-700 mb-2">支付密码</label>
                    <div class="relative">
                        <input type="password" id="paymentPassword" placeholder="请输入6位支付密码" 
                               class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-center text-lg font-bold tracking-widest"
                               maxlength="6" oninput="validatePassword()">
                        <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggle"></i>
                        </button>
                    </div>
                </div>
                

            </div>
        </div>

        <!-- 提现按钮 -->
        <div class="px-6 mb-6">
            <button id="withdrawButton" class="w-full bg-gray-200 text-gray-500 font-semibold py-4 px-6 rounded-xl cursor-not-allowed transition-all" disabled>
                <i class="fas fa-lock mr-2"></i>请完善提现信息
            </button>
        </div>

        <!-- 提现记录 -->
        <div class="px-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg fade-in">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-history text-gray-500 mr-2"></i>最近提现记录
                    </h3>
                    <button class="text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors">
                        查看全部
                    </button>
                </div>
                
                <div class="space-y-3">
                    <!-- 提现记录项 -->
                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-check text-green-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">USDT提现</p>
                                <p class="text-sm text-gray-500">TRC20网络 • 已到账</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">-50.00 USDT</p>
                            <p class="text-xs text-gray-500">2024-07-28</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-clock text-yellow-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">USDT提现</p>
                                <p class="text-sm text-gray-500">TRC20网络 • 处理中</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">-100.00 USDT</p>
                            <p class="text-xs text-gray-500">2024-07-29</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="px-6 pb-6">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-3 mt-0.5"></i>
                    <div>
                        <p class="text-red-900 text-sm font-medium mb-2">安全提示</p>
                        <ul class="text-red-800 text-sm space-y-1">
                            <li>• 请仔细核对提现地址，转账无法撤销</li>
                            <li>• 提现将在1-24小时内处理完成</li>
                            <li>• 如有异常请及时联系客服</li>
                            <li>• 请勿向他人透露支付密码和验证码</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义提示框 -->
    <div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                    <i id="alertIconClass" class="text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="alertMessage" class="text-gray-600 mb-6"></p>
                <button id="alertButton" class="w-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-indigo-600 hover:to-violet-600 transition-all">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="customConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-orange-100">
                    <i class="fas fa-exclamation-triangle text-2xl text-orange-600"></i>
                </div>
                <h3 id="confirmTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="confirmMessage" class="text-gray-600 mb-6"></p>
                <div class="flex space-x-3">
                    <button id="cancelButton" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                        取消
                    </button>
                    <button id="confirmButton" class="flex-1 bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-red-600 transition-all">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        let selectedNetwork = 'trc20';

        // 检查用户是否已设置支付密码
        function checkPaymentPasswordStatus() {
            // 从localStorage检查支付密码状态
            const hasPaymentPassword = localStorage.getItem('hasPaymentPassword') === 'true';

            if (!hasPaymentPassword) {
                // 显示设置支付密码提示
                showPaymentPasswordPrompt();
                return false;
            }
            return true;
        }

        // 显示设置支付密码提示弹窗
        function showPaymentPasswordPrompt() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-sm w-full transform scale-95 transition-transform">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt text-orange-500 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">安全验证</h3>
                        <p class="text-gray-600">为了保障您的资金安全，提现前需要设置支付密码</p>
                    </div>

                    <div class="space-y-3">
                        <button onclick="goToSetPaymentPassword()" class="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-orange-600 hover:to-red-600 transition-all">
                            <i class="fas fa-key mr-2"></i>立即设置支付密码
                        </button>
                        <button onclick="goBackToWallet()" class="w-full bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                            <i class="fas fa-arrow-left mr-2"></i>返回钱包
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 显示动画
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
        }

        // 跳转到设置支付密码页面
        function goToSetPaymentPassword() {
            // 设置来源标记，用于设置完成后返回提现页面
            localStorage.setItem('paymentPasswordSource', 'withdraw');
            window.location.href = 'payment-password.html';
        }
        
        // 自定义提示框
        function showAlert(title, message, type = 'info') {
            const alertBox = document.getElementById('customAlert');
            const alertIcon = document.getElementById('alertIcon');
            const alertIconClass = document.getElementById('alertIconClass');
            const alertTitle = document.getElementById('alertTitle');
            const alertMessage = document.getElementById('alertMessage');
            const alertButton = document.getElementById('alertButton');
            
            // 设置图标和颜色
            switch(type) {
                case 'success':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-green-100';
                    alertIconClass.className = 'fas fa-check text-2xl text-green-600';
                    break;
                case 'error':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-red-100';
                    alertIconClass.className = 'fas fa-times text-2xl text-red-600';
                    break;
                case 'warning':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-yellow-100';
                    alertIconClass.className = 'fas fa-exclamation-triangle text-2xl text-yellow-600';
                    break;
                default:
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-blue-100';
                    alertIconClass.className = 'fas fa-info-circle text-2xl text-blue-600';
            }
            
            alertTitle.textContent = title;
            alertMessage.textContent = message;
            
            // 显示提示框
            alertBox.classList.remove('hidden');
            setTimeout(() => {
                alertBox.querySelector('.bg-white').classList.remove('scale-95');
                alertBox.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // 绑定关闭事件
            alertButton.onclick = () => hideAlert();
            alertBox.onclick = (e) => {
                if (e.target === alertBox) hideAlert();
            };
        }
        
        function hideAlert() {
            const alertBox = document.getElementById('customAlert');
            alertBox.querySelector('.bg-white').classList.remove('scale-100');
            alertBox.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                alertBox.classList.add('hidden');
            }, 300);
        }
        
        // 自定义确认对话框
        function showConfirm(title, message, onConfirm) {
            const confirmBox = document.getElementById('customConfirm');
            const confirmTitle = document.getElementById('confirmTitle');
            const confirmMessage = document.getElementById('confirmMessage');
            const confirmButton = document.getElementById('confirmButton');
            const cancelButton = document.getElementById('cancelButton');
            
            confirmTitle.textContent = title;
            confirmMessage.textContent = message;
            
            // 显示确认框
            confirmBox.classList.remove('hidden');
            setTimeout(() => {
                confirmBox.querySelector('.bg-white').classList.remove('scale-95');
                confirmBox.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // 绑定事件
            confirmButton.onclick = () => {
                hideConfirm();
                onConfirm();
            };
            
            cancelButton.onclick = () => hideConfirm();
            confirmBox.onclick = (e) => {
                if (e.target === confirmBox) hideConfirm();
            };
        }
        
        function hideConfirm() {
            const confirmBox = document.getElementById('customConfirm');
            confirmBox.querySelector('.bg-white').classList.remove('scale-100');
            confirmBox.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                confirmBox.classList.add('hidden');
            }, 300);
        }
        
        // 设置提现金额
        function setAmount(amount) {
            document.getElementById('withdrawAmount').value = amount;
            calculateFee();
        }
        
        // 计算手续费
        function calculateFee() {
            const amount = parseFloat(document.getElementById('withdrawAmount').value) || 0;
            const fee = selectedNetwork === 'trc20' ? 2 : 15;
            const actualAmount = Math.max(0, amount - fee);
            
            if (amount > 0) {
                document.getElementById('feeCalculation').classList.remove('hidden');
                document.getElementById('withdrawAmountDisplay').textContent = amount + ' USDT';
                document.getElementById('feeDisplay').textContent = fee + ' USDT';
                document.getElementById('actualAmountDisplay').textContent = actualAmount + ' USDT';
            } else {
                document.getElementById('feeCalculation').classList.add('hidden');
            }
            
            validateForm();
        }
        
        // 选择网络
        function selectNetwork(network) {
            selectedNetwork = network;
            
            // 更新网络选择状态
            document.querySelectorAll('.network-option').forEach(option => {
                option.classList.remove('border-indigo-200', 'bg-indigo-50');
                option.classList.add('border-gray-200', 'bg-gray-50');
                
                const radio = option.querySelector('.w-2.h-2');
                if (radio) radio.style.display = 'none';
            });
            
            const selectedOption = event.currentTarget;
            selectedOption.classList.remove('border-gray-200', 'bg-gray-50');
            selectedOption.classList.add('border-indigo-200', 'bg-indigo-50');
            
            const radio = selectedOption.querySelector('.w-2.h-2');
            if (radio) radio.style.display = 'block';
            
            calculateFee();
        }
        
        // 粘贴地址
        function pasteAddress() {
            navigator.clipboard.readText().then(text => {
                document.getElementById('withdrawAddress').value = text;
                validateAddress();
            });
        }
        
        // 验证地址
        function validateAddress() {
            const address = document.getElementById('withdrawAddress').value;
            const validation = document.getElementById('addressValidation');
            
            if (address.length > 20) {
                validation.classList.remove('hidden');
            } else {
                validation.classList.add('hidden');
            }
            
            validateForm();
        }
        
        // 验证支付密码
        function validatePassword() {
            validateForm();
        }
        
        // 切换密码显示
        function togglePassword() {
            const input = document.getElementById('paymentPassword');
            const icon = document.getElementById('passwordToggle');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        

        
        // 验证表单
        function validateForm() {
            const amount = parseFloat(document.getElementById('withdrawAmount').value) || 0;
            const address = document.getElementById('withdrawAddress').value;
            const password = document.getElementById('paymentPassword').value;
            
            const button = document.getElementById('withdrawButton');
            
            if (amount >= 10 && address.length > 20 && password.length === 6) {
                button.classList.remove('bg-gray-200', 'text-gray-500', 'cursor-not-allowed');
                button.classList.add('bg-gradient-to-r', 'from-red-500', 'to-pink-500', 'text-white', 'hover:from-red-600', 'hover:to-pink-600');
                button.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>确认提现';
                button.disabled = false;
                button.onclick = submitWithdraw;
            } else {
                button.classList.add('bg-gray-200', 'text-gray-500', 'cursor-not-allowed');
                button.classList.remove('bg-gradient-to-r', 'from-red-500', 'to-pink-500', 'text-white', 'hover:from-red-600', 'hover:to-pink-600');
                button.innerHTML = '<i class="fas fa-lock mr-2"></i>请完善提现信息';
                button.disabled = true;
                button.onclick = null;
            }
        }
        
        // 提交提现
        function submitWithdraw() {
            // 验证支付密码
            const inputPassword = document.getElementById('paymentPassword').value;
            const savedPassword = localStorage.getItem('paymentPassword');

            if (inputPassword !== savedPassword) {
                showAlert('密码错误', '支付密码不正确，请重新输入', 'error');
                return;
            }

            showConfirm('确认提现', '提现后无法撤销，确定要继续吗？', () => {
                // 模拟提现处理
                const button = document.getElementById('withdrawButton');
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
                button.disabled = true;

                setTimeout(() => {
                    showAlert('提现成功', '提现申请已提交！预计1-24小时内到账，请耐心等待。', 'success');
                    setTimeout(() => {
                        goBackToWallet();
                    }, 2000);
                }, 2000);
            });
        }
        
        // 地址输入监听
        document.getElementById('withdrawAddress').addEventListener('input', validateAddress);
        
        // 返回钱包页面
        function goBackToWallet() {
            // 直接返回到钱包Tab页面
            window.location.href = 'index.html#wallet';
        }
        
        // 显示地址簿
        function showAddressBook() {
            window.location.href = 'address-book.html';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 先检查支付密码状态
            if (checkPaymentPasswordStatus()) {
                // 如果已设置支付密码，正常初始化页面
                selectNetwork('trc20');
                checkSelectedAddress();
                loadQuickAddresses();
            }
        });

        // 加载快速地址选择
        function loadQuickAddresses() {
            const saved = localStorage.getItem('savedAddresses');
            if (saved) {
                const addresses = JSON.parse(saved);
                const currentNetwork = document.querySelector('.network-btn.border-indigo-500')?.dataset.network || 'trc20';

                // 过滤当前网络的地址，最多显示3个
                const filteredAddresses = addresses
                    .filter(addr => addr.network === currentNetwork)
                    .slice(0, 3);

                if (filteredAddresses.length > 0) {
                    renderQuickAddresses(filteredAddresses);
                    document.getElementById('quickAddresses').classList.remove('hidden');
                } else {
                    document.getElementById('quickAddresses').classList.add('hidden');
                }
            }
        }

        // 渲染快速地址
        function renderQuickAddresses(addresses) {
            const quickAddressList = document.getElementById('quickAddressList');
            quickAddressList.innerHTML = addresses.map((addr, index) => `
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-3 hover:bg-gray-100 transition-colors cursor-pointer"
                     onclick="selectQuickAddress('${addr.address}', '${addr.label}')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-wallet text-indigo-600 text-xs"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${addr.label}</div>
                                <div class="text-xs text-gray-500 font-mono">${addr.address.substring(0, 8)}...${addr.address.substring(addr.address.length - 8)}</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                </div>
            `).join('');
        }

        // 选择快速地址
        function selectQuickAddress(address, label) {
            document.getElementById('withdrawAddress').value = address;
            validateAddress();
            showAddressSelectedTip(label);
        }

        // 更新网络选择时重新加载快速地址
        const originalSelectNetwork = selectNetwork;
        selectNetwork = function(network) {
            originalSelectNetwork(network);
            setTimeout(loadQuickAddresses, 100); // 延迟加载以确保网络选择完成
        };

        // 检查是否有从地址簿选择的地址
        function checkSelectedAddress() {
            const selectedAddress = localStorage.getItem('selectedAddress');
            if (selectedAddress) {
                const addr = JSON.parse(selectedAddress);

                // 设置网络类型
                selectNetwork(addr.network);

                // 设置地址
                document.getElementById('withdrawAddress').value = addr.address;

                // 验证地址
                validateAddress();

                // 清除选中的地址
                localStorage.removeItem('selectedAddress');

                // 显示提示
                showAddressSelectedTip(addr.label);
            }
        }

        // 显示地址选择提示
        function showAddressSelectedTip(label) {
            // 创建提示元素
            const tip = document.createElement('div');
            tip.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 text-sm';
            tip.innerHTML = `<i class="fas fa-check mr-2"></i>已选择地址：${label}`;
            document.body.appendChild(tip);

            // 3秒后自动消失
            setTimeout(() => {
                tip.style.opacity = '0';
                tip.style.transform = 'translate(-50%, -20px)';
                tip.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(tip);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>