<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 密码强度指示器 */
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .strength-weak { background-color: #ef4444; width: 33%; }
        .strength-medium { background-color: #f59e0b; width: 66%; }
        .strength-strong { background-color: #10b981; width: 100%; }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-6 pb-8">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-xl font-bold">修改密码</h1>
                <div class="w-6"></div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="px-6 py-6 -mt-4">
            <!-- 安全提示 -->
            <div class="bg-blue-50 border border-blue-200 rounded-2xl p-4 mb-6 fade-in">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                        <i class="fas fa-info-circle text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-blue-900 mb-1">安全提示</h4>
                        <p class="text-sm text-blue-700">为了您的账户安全，请设置一个强密码。密码应包含大小写字母、数字和特殊字符。</p>
                    </div>
                </div>
            </div>

            <!-- 修改密码表单 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">
                    <i class="fas fa-key text-indigo-500 mr-2"></i>密码设置
                </h3>
                
                <form id="passwordForm">
                    <!-- 当前密码 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                        <div class="relative">
                            <input type="password" id="currentPassword" placeholder="请输入当前密码" 
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                            <button type="button" onclick="togglePassword('currentPassword')" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-eye" id="currentPasswordToggle"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 新密码 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                        <div class="relative">
                            <input type="password" id="newPassword" placeholder="请输入新密码" 
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                oninput="checkPasswordStrength()">
                            <button type="button" onclick="togglePassword('newPassword')" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-eye" id="newPasswordToggle"></i>
                            </button>
                        </div>
                        
                        <!-- 密码强度指示器 -->
                        <div class="mt-3">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-xs text-gray-500">密码强度</span>
                                <span class="text-xs" id="strengthText">请输入密码</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1">
                                <div class="strength-bar" id="strengthBar"></div>
                            </div>
                        </div>

                        <!-- 密码要求 -->
                        <div class="mt-3 space-y-1">
                            <div class="flex items-center text-xs" id="lengthCheck">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                <span class="text-gray-500">至少8个字符</span>
                            </div>
                            <div class="flex items-center text-xs" id="uppercaseCheck">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                <span class="text-gray-500">包含大写字母</span>
                            </div>
                            <div class="flex items-center text-xs" id="lowercaseCheck">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                <span class="text-gray-500">包含小写字母</span>
                            </div>
                            <div class="flex items-center text-xs" id="numberCheck">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                <span class="text-gray-500">包含数字</span>
                            </div>
                            <div class="flex items-center text-xs" id="specialCheck">
                                <i class="fas fa-times text-red-500 mr-2"></i>
                                <span class="text-gray-500">包含特殊字符</span>
                            </div>
                        </div>
                    </div>

                    <!-- 确认新密码 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                        <div class="relative">
                            <input type="password" id="confirmPassword" placeholder="请再次输入新密码" 
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                                oninput="checkPasswordMatch()">
                            <button type="button" onclick="togglePassword('confirmPassword')" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-eye" id="confirmPasswordToggle"></i>
                            </button>
                        </div>
                        <div id="matchMessage" class="mt-2 text-xs hidden">
                            <i class="fas fa-times text-red-500 mr-1"></i>
                            <span class="text-red-500">两次输入的密码不一致</span>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <button type="submit" id="submitButton" 
                        class="w-full bg-gray-200 text-gray-500 font-semibold py-4 px-6 rounded-xl cursor-not-allowed transition-all" disabled>
                        <i class="fas fa-lock mr-2"></i>请完善密码信息
                    </button>
                </form>
            </div>

            <!-- 密码安全建议 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>密码安全建议
                </h3>
                
                <div class="space-y-3">
                    <div class="flex items-start">
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <i class="fas fa-check text-green-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 text-sm">使用复杂密码</div>
                            <div class="text-xs text-gray-500">包含大小写字母、数字和特殊字符</div>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <i class="fas fa-check text-green-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 text-sm">定期更换密码</div>
                            <div class="text-xs text-gray-500">建议每3-6个月更换一次密码</div>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                            <i class="fas fa-check text-green-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 text-sm">避免使用个人信息</div>
                            <div class="text-xs text-gray-500">不要使用生日、姓名等容易猜测的信息</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示弹窗 -->
    <div id="alertModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-xl font-bold text-gray-900 mb-2">修改成功</h3>
                <p id="alertMessage" class="text-gray-600 text-sm mb-6">密码已成功修改</p>
                
                <button onclick="closeAlertModal()" 
                    class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script>
        let passwordStrength = 0;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听表单输入
            document.getElementById('currentPassword').addEventListener('input', validateForm);
            document.getElementById('newPassword').addEventListener('input', validateForm);
            document.getElementById('confirmPassword').addEventListener('input', validateForm);

            // 初始化密码强度检查
            checkPasswordStrength();
        });

        // 切换密码显示
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const toggle = document.getElementById(fieldId + 'Toggle');
            
            if (field.type === 'password') {
                field.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }

        // 检查密码强度
        function checkPasswordStrength() {
            const password = document.getElementById('newPassword').value;
            const strengthBar = document.getElementById('strengthBar');
            const strengthText = document.getElementById('strengthText');
            
            let score = 0;
            let feedback = '';
            
            // 检查各项要求
            const hasLength = password.length >= 8;
            const hasUppercase = /[A-Z]/.test(password);
            const hasLowercase = /[a-z]/.test(password);
            const hasNumber = /\d/.test(password);
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            
            // 更新检查项显示
            updateCheck('lengthCheck', hasLength);
            updateCheck('uppercaseCheck', hasUppercase);
            updateCheck('lowercaseCheck', hasLowercase);
            updateCheck('numberCheck', hasNumber);
            updateCheck('specialCheck', hasSpecial);
            
            // 计算强度分数
            if (hasLength) score++;
            if (hasUppercase) score++;
            if (hasLowercase) score++;
            if (hasNumber) score++;
            if (hasSpecial) score++;
            
            // 设置强度显示
            if (password.length === 0) {
                strengthBar.className = 'strength-bar';
                strengthText.textContent = '请输入密码';
                strengthText.className = 'text-xs text-gray-500';
                passwordStrength = 0;
            } else if (score < 3) {
                strengthBar.className = 'strength-bar strength-weak';
                strengthText.textContent = '弱';
                strengthText.className = 'text-xs text-red-500';
                passwordStrength = 1;
            } else if (score < 5) {
                strengthBar.className = 'strength-bar strength-medium';
                strengthText.textContent = '中等';
                strengthText.className = 'text-xs text-yellow-500';
                passwordStrength = 2;
            } else {
                strengthBar.className = 'strength-bar strength-strong';
                strengthText.textContent = '强';
                strengthText.className = 'text-xs text-green-500';
                passwordStrength = 3;
            }
            
            validateForm();
        }

        // 更新检查项显示
        function updateCheck(checkId, passed) {
            const check = document.getElementById(checkId);
            const icon = check.querySelector('i');
            const text = check.querySelector('span');
            
            if (passed) {
                icon.className = 'fas fa-check text-green-500 mr-2';
                text.className = 'text-green-500';
            } else {
                icon.className = 'fas fa-times text-red-500 mr-2';
                text.className = 'text-gray-500';
            }
        }

        // 检查密码匹配
        function checkPasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchMessage = document.getElementById('matchMessage');
            
            if (confirmPassword.length > 0) {
                if (newPassword === confirmPassword) {
                    matchMessage.innerHTML = '<i class="fas fa-check text-green-500 mr-1"></i><span class="text-green-500">密码匹配</span>';
                    matchMessage.classList.remove('hidden');
                } else {
                    matchMessage.innerHTML = '<i class="fas fa-times text-red-500 mr-1"></i><span class="text-red-500">两次输入的密码不一致</span>';
                    matchMessage.classList.remove('hidden');
                }
            } else {
                matchMessage.classList.add('hidden');
            }
            
            validateForm();
        }

        // 验证表单
        function validateForm() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const submitButton = document.getElementById('submitButton');
            
            const isValid = currentPassword.length > 0 && 
                           passwordStrength >= 2 && 
                           newPassword === confirmPassword && 
                           confirmPassword.length > 0;
            
            if (isValid) {
                submitButton.className = 'w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg';
                submitButton.innerHTML = '<i class="fas fa-key mr-2"></i>修改密码';
                submitButton.disabled = false;
            } else {
                submitButton.className = 'w-full bg-gray-200 text-gray-500 font-semibold py-4 px-6 rounded-xl cursor-not-allowed transition-all';
                submitButton.innerHTML = '<i class="fas fa-lock mr-2"></i>请完善密码信息';
                submitButton.disabled = true;
            }
        }

        // 表单提交
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            
            // 验证当前密码（模拟）
            if (currentPassword !== '123456') {
                showAlert('密码错误', '当前密码不正确，请重新输入', 'error');
                return;
            }
            
            // 模拟修改过程
            const submitButton = document.getElementById('submitButton');
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>修改中...';
            submitButton.disabled = true;
            
            setTimeout(() => {
                showAlert('修改成功', '密码已成功修改，请使用新密码登录', 'success');
                
                // 3秒后返回上一页
                setTimeout(() => {
                    goBack();
                }, 3000);
            }, 2000);
        });

        // 显示提示弹窗
        function showAlert(title, message, type = 'success') {
            const modal = document.getElementById('alertModal');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');
            
            titleEl.textContent = title;
            messageEl.textContent = message;
            
            if (type === 'success') {
                icon.className = 'w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-check-circle text-green-600 text-2xl"></i>';
            } else if (type === 'error') {
                icon.className = 'w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>';
            }
            
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭提示弹窗
        function closeAlertModal() {
            const modal = document.getElementById('alertModal');
            const modalContent = modal.querySelector('.bg-white');
            
            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const alertModal = document.getElementById('alertModal');
            if (e.target === alertModal) {
                closeAlertModal();
            }
        });
    </script>
</body>
</html>
