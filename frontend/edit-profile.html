<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑资料 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-6 pb-8">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-xl font-bold">编辑资料</h1>
                <button onclick="saveProfile()" class="text-white hover:text-gray-200 transition-colors font-medium">
                    保存
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="px-6 py-6 -mt-4">
            <!-- 头像设置 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-user-circle text-indigo-500 mr-2"></i>头像设置
                </h3>
                
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                        <button onclick="changeAvatar()" class="absolute -bottom-1 -right-1 w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center text-white hover:bg-indigo-600 transition-colors">
                            <i class="fas fa-camera text-xs"></i>
                        </button>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900">更换头像</h4>
                        <p class="text-sm text-gray-500">点击相机图标上传新头像</p>
                    </div>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-id-card text-green-500 mr-2"></i>基本信息
                </h3>
                
                <div class="space-y-4">
                    <!-- 用户名 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                        <input type="text" id="username" value="user123456" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                    </div>

                    <!-- 昵称 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">昵称</label>
                        <input type="text" id="nickname" value="TikTok达人" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                    </div>

                    <!-- 邮箱 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
                        <div class="relative">
                            <input type="email" id="email" value="<EMAIL>" 
                                class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">已验证</span>
                            </div>
                        </div>
                    </div>


                </div>
            </div>

            <!-- 个人简介 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-edit text-blue-500 mr-2"></i>个人简介
                </h3>
                
                <div>
                    <textarea id="bio" rows="4" placeholder="介绍一下自己吧..." 
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all resize-none">热爱TikTok，专业任务达人，每日完成高质量任务，期待与大家一起成长！</textarea>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-xs text-gray-500">最多200字</span>
                        <span class="text-xs text-gray-400" id="bioCount">42/200</span>
                    </div>
                </div>
            </div>

            <!-- 社交账号 -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 fade-in">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-share-alt text-pink-500 mr-2"></i>社交账号
                </h3>
                
                <div class="space-y-4">
                    <!-- TikTok账号 -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fab fa-tiktok text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">TikTok</div>
                                <div class="text-sm text-gray-500">@my_tiktok_account</div>
                            </div>
                        </div>
                        <button onclick="editSocialAccount('tiktok')" class="text-indigo-600 hover:text-indigo-800 transition-colors">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- 提示弹窗 -->
    <div id="alertModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-xl font-bold text-gray-900 mb-2">保存成功</h3>
                <p id="alertMessage" class="text-gray-600 text-sm mb-6">您的资料已更新</p>
                
                <button onclick="closeAlertModal()" 
                    class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBioCount();
            
            // 监听简介输入
            document.getElementById('bio').addEventListener('input', updateBioCount);
        });

        // 更新简介字数统计
        function updateBioCount() {
            const bio = document.getElementById('bio');
            const count = document.getElementById('bioCount');
            const length = bio.value.length;
            count.textContent = `${length}/200`;
            
            if (length > 200) {
                count.classList.add('text-red-500');
                bio.value = bio.value.substring(0, 200);
                count.textContent = '200/200';
            } else {
                count.classList.remove('text-red-500');
            }
        }

        // 更换头像
        function changeAvatar() {
            showAlert('功能开发中', '头像上传功能正在开发中，敬请期待！', 'info');
        }

        // 编辑社交账号
        function editSocialAccount(platform) {
            showAlert('功能开发中', '社交账号绑定功能正在开发中，敬请期待！', 'info');
        }

        // 保存资料
        function saveProfile() {
            // 验证表单
            const username = document.getElementById('username').value.trim();
            const nickname = document.getElementById('nickname').value.trim();
            const email = document.getElementById('email').value.trim();
            
            if (!username || !nickname || !email) {
                showAlert('信息不完整', '请填写完整的基本信息', 'warning');
                return;
            }
            
            // 模拟保存过程
            showAlert('保存成功', '您的资料已更新', 'success');
            
            // 2秒后自动关闭
            setTimeout(() => {
                closeAlertModal();
            }, 2000);
        }

        // 显示提示弹窗
        function showAlert(title, message, type = 'success') {
            const modal = document.getElementById('alertModal');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');
            
            titleEl.textContent = title;
            messageEl.textContent = message;
            
            if (type === 'success') {
                icon.className = 'w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-check-circle text-green-600 text-2xl"></i>';
            } else if (type === 'warning') {
                icon.className = 'w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>';
            } else if (type === 'info') {
                icon.className = 'w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-info-circle text-blue-600 text-2xl"></i>';
            }
            
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭提示弹窗
        function closeAlertModal() {
            const modal = document.getElementById('alertModal');
            const modalContent = modal.querySelector('.bg-white');
            
            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const alertModal = document.getElementById('alertModal');
            if (e.target === alertModal) {
                closeAlertModal();
            }
        });
    </script>
</body>
</html>
