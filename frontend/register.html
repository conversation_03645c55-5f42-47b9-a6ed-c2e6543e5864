<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
    </style>
</head>

<body class="bg-gradient-to-br from-indigo-50 to-violet-50 min-h-screen">
    <div class="flex flex-col justify-center min-h-screen px-6 py-8">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div
                class="bg-gradient-to-r from-indigo-500 to-violet-500 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fab fa-tiktok text-white text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">创建账户</h1>
            <p class="text-gray-600">开启你的TikTok任务赚钱之旅</p>
        </div>

        <!-- 注册表单 -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <form class="space-y-5">
                <!-- 邮箱输入 -->
                <div>
                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-indigo-500"></i>邮箱地址
                    </label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址"
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200">
                </div>

                <!-- 密码输入 -->
                <div>
                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-indigo-500"></i>设置密码
                    </label>
                    <div class="relative">
                        <input type="password" id="password" name="password" placeholder="请设置密码（至少6位）"
                            class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200">
                        <button type="button"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label for="confirmPassword" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-check-circle mr-2 text-indigo-500"></i>确认密码
                    </label>
                    <div class="relative">
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码"
                            class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200">
                        <button type="button"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- 邀请码输入 -->
                <div>
                    <label for="referralCode" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-gift mr-2 text-violet-500"></i>邀请码 (可选)
                    </label>
                    <input type="text" id="referralCode" name="referralCode" placeholder="输入邀请码获得奖励"
                        class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200">
                </div>

                <!-- 服务条款 -->
                <div class="flex items-start space-x-3">
                    <input type="checkbox" id="terms" name="terms"
                        class="mt-1 w-5 h-5 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" checked>
                    <label for="terms" class="text-sm text-gray-600">
                        我已阅读并同意
                        <a href="#" class="text-indigo-600 hover:underline">《用户协议》</a>
                        和
                        <a href="#" class="text-indigo-600 hover:underline">《隐私政策》</a>
                    </label>
                </div>

                <!-- 注册按钮 -->
                <button type="submit"
                    class="w-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white font-semibold py-4 px-6 rounded-xl hover:from-indigo-600 hover:to-violet-600 transition-all duration-200 shadow-lg"
                    onclick="handleRegister(event)">
                    <i class="fas fa-user-plus mr-2"></i>立即注册
                </button>
            </form>
        </div>

        <!-- 登录链接 -->
        <div class="text-center">
            <p class="text-gray-600">
                已有账户？
                <a href="login.html" class="text-indigo-600 hover:text-indigo-800 font-semibold ml-1">
                    立即登录
                </a>
            </p>
        </div>

        <!-- 注册优势 -->
        <div class="mt-8 bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">
                <i class="fas fa-star text-yellow-500 mr-2"></i>注册即享特权
            </h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-coins text-green-600 text-xl"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-700">新人奖励</p>
                    <p class="text-xs text-gray-500">10 USDT</p>
                </div>
                <div class="text-center">
                    <div class="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-tasks text-blue-600 text-xl"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-700">免费任务</p>
                    <p class="text-xs text-gray-500">每日更新</p>
                </div>
                <div class="text-center">
                    <div class="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-700">推荐返佣</p>
                    <p class="text-xs text-gray-500">三级分销</p>
                </div>
                <div class="text-center">
                    <div class="bg-yellow-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-crown text-yellow-600 text-xl"></i>
                    </div>
                    <p class="text-sm font-medium text-gray-700">VIP特权</p>
                    <p class="text-xs text-gray-500">高额任务</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义提示框 -->
    <div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                    <i id="alertIconClass" class="text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="alertMessage" class="text-gray-600 mb-6"></p>
                <button id="alertButton" class="w-full bg-gradient-to-r from-indigo-500 to-violet-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-indigo-600 hover:to-violet-600 transition-all">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 自定义提示框
        function showAlert(title, message, type = 'info') {
            const alertBox = document.getElementById('customAlert');
            const alertIcon = document.getElementById('alertIcon');
            const alertIconClass = document.getElementById('alertIconClass');
            const alertTitle = document.getElementById('alertTitle');
            const alertMessage = document.getElementById('alertMessage');
            const alertButton = document.getElementById('alertButton');
            
            // 设置图标和颜色
            switch(type) {
                case 'success':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-green-100';
                    alertIconClass.className = 'fas fa-check text-2xl text-green-600';
                    break;
                case 'error':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-red-100';
                    alertIconClass.className = 'fas fa-times text-2xl text-red-600';
                    break;
                case 'warning':
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-yellow-100';
                    alertIconClass.className = 'fas fa-exclamation-triangle text-2xl text-yellow-600';
                    break;
                default:
                    alertIcon.className = 'w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-blue-100';
                    alertIconClass.className = 'fas fa-info-circle text-2xl text-blue-600';
            }
            
            alertTitle.textContent = title;
            alertMessage.textContent = message;
            
            // 显示提示框
            alertBox.classList.remove('hidden');
            setTimeout(() => {
                alertBox.querySelector('.bg-white').classList.remove('scale-95');
                alertBox.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // 绑定关闭事件
            alertButton.onclick = () => hideAlert();
            alertBox.onclick = (e) => {
                if (e.target === alertBox) hideAlert();
            };
        }
        
        function hideAlert() {
            const alertBox = document.getElementById('customAlert');
            alertBox.querySelector('.bg-white').classList.remove('scale-100');
            alertBox.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                alertBox.classList.add('hidden');
            }, 300);
        }
        
        // 处理注册
        function handleRegister(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const inviteCode = document.getElementById('inviteCode').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;

            // 表单验证
            if (!email || !password || !confirmPassword) {
                showAlert('提示', '请填写完整的注册信息', 'warning');
                return;
            }

            if (password !== confirmPassword) {
                showAlert('错误', '两次输入的密码不一致', 'error');
                return;
            }

            if (password.length < 6) {
                showAlert('密码太短', '密码长度至少6位', 'warning');
                return;
            }

            if (!agreeTerms) {
                showAlert('请同意协议', '请同意用户协议和隐私政策', 'warning');
                return;
            }

            // 模拟注册过程
            const button = event.target;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>注册中...';
            button.disabled = true;

            setTimeout(() => {
                // 模拟注册成功
                const userInfo = {
                    email: email,
                    nickname: '新用户',
                    vipLevel: 1,
                    balance: 0,
                    inviteCode: inviteCode || '',
                    registerTime: new Date().toISOString()
                };

                // 保存用户信息
                localStorage.setItem('userToken', 'demo_token_' + Date.now());
                localStorage.setItem('userInfo', JSON.stringify(userInfo));

                showAlert('注册成功', '账户创建成功！即将跳转到主页面', 'success');

                // 延迟跳转
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            }, 2000);
        }

        // 切换密码显示
        function togglePassword(inputId, iconElement) {
            const passwordInput = document.getElementById(inputId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                iconElement.classList.remove('fa-eye');
                iconElement.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                iconElement.classList.remove('fa-eye-slash');
                iconElement.classList.add('fa-eye');
            }
        }

        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function () {
            const userToken = localStorage.getItem('userToken');
            if (userToken) {
                // 如果已登录，直接跳转到主页面
                window.location.href = 'index.html';
            }

            // 为密码显示切换按钮添加事件监听
            document.querySelectorAll('.fa-eye').forEach(icon => {
                icon.parentElement.addEventListener('click', function () {
                    const inputId = this.previousElementSibling.id;
                    togglePassword(inputId, icon);
                });
            });
        });
    </script>
</body>

</html>