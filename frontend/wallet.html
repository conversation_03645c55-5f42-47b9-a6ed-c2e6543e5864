<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的钱包 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .usdt-icon {
            background: linear-gradient(135deg, #26A17B 0%, #22C55E 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm">
        <div class="flex items-center justify-between p-4">
            <h1 class="text-xl font-bold text-gray-900">我的钱包</h1>
            <div class="flex items-center space-x-3">
                <button class="p-2 text-gray-600 hover:text-gray-900">
                    <i class="fas fa-history text-lg"></i>
                </button>
                <button class="p-2 text-gray-600 hover:text-gray-900">
                    <i class="fas fa-cog text-lg"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 余额卡片 -->
    <div class="p-6">
        <div class="balance-card rounded-2xl p-6 text-white mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <p class="text-white/80 text-sm">总余额</p>
                    <div class="flex items-center mt-1">
                        <div class="usdt-icon w-8 h-8 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">₮</span>
                        </div>
                        <span class="text-3xl font-bold">128.56</span>
                        <span class="text-lg ml-2 opacity-90">USDT</span>
                    </div>
                </div>
                <button class="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors">
                    <i class="fas fa-eye text-xl"></i>
                </button>
            </div>

            <!-- 今日收益 -->
            <div class="bg-white/20 rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm">今日收益</p>
                        <p class="text-xl font-semibold text-green-300">+15.75 USDT</p>
                    </div>
                    <div class="text-right">
                        <p class="text-white/80 text-sm">昨日收益</p>
                        <p class="text-lg font-medium">+12.30 USDT</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <button class="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:from-green-600 hover:to-emerald-600 transition-all" onclick="window.location.href='deposit.html'">
                <i class="fas fa-plus-circle mb-2 text-xl"></i>
                <p>充值</p>
            </button>
            <button class="bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:from-blue-600 hover:to-indigo-600 transition-all" onclick="window.location.href='withdraw.html'">
                <i class="fas fa-arrow-up mb-2 text-xl"></i>
                <p>提现</p>
            </button>
        </div>

        <!-- 统计信息 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-chart-line text-indigo-500 mr-2"></i>收益统计
            </h3>
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">¥2,456</p>
                    <p class="text-sm text-gray-500">本月收益</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600">¥8,923</p>
                    <p class="text-sm text-gray-500">累计收益</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-purple-600">156</p>
                    <p class="text-sm text-gray-500">完成任务</p>
                </div>
            </div>
        </div>

        <!-- 交易记录 -->
        <div class="bg-white rounded-2xl p-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-receipt text-indigo-500 mr-2"></i>交易记录
                </h3>
                <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    查看全部
                </button>
            </div>

            <!-- 交易记录列表 -->
            <div class="space-y-4">
                <!-- 任务收益 -->
                <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-plus text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">任务收益</p>
                            <p class="text-sm text-gray-500">TikTok点赞任务</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+2.50 USDT</p>
                        <p class="text-xs text-gray-500">刚刚</p>
                    </div>
                </div>

                <!-- 充值记录 -->
                <div class="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-arrow-down text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">USDT充值</p>
                            <p class="text-sm text-gray-500">钱包充值</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-blue-600">+100.00 USDT</p>
                        <p class="text-xs text-gray-500">2小时前</p>
                    </div>
                </div>

                <!-- 提现记录 -->
                <div class="flex items-center justify-between p-4 bg-orange-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-arrow-up text-orange-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">USDT提现</p>
                            <p class="text-sm text-gray-500">提现到钱包</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-orange-600">-50.00 USDT</p>
                        <p class="text-xs text-gray-500">昨天</p>
                    </div>
                </div>

                <!-- 推荐奖励 -->
                <div class="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-gift text-purple-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">推荐奖励</p>
                            <p class="text-sm text-gray-500">三级分销奖励</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-purple-600">+5.20 USDT</p>
                        <p class="text-xs text-gray-500">2天前</p>
                    </div>
                </div>

                <!-- VIP购买 -->
                <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-crown text-yellow-600"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">VIP升级</p>
                            <p class="text-sm text-gray-500">购买VIP2等级</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-yellow-600">-30.00 USDT</p>
                        <p class="text-xs text-gray-500">3天前</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div class="flex items-start">
                <i class="fas fa-shield-alt text-blue-500 mr-3 mt-1"></i>
                <div>
                    <h4 class="font-semibold text-blue-900 mb-1">
                        <i class="fas fa-lock mr-1"></i>资金安全
                    </h4>
                    <ul class="text-blue-800 text-sm space-y-1">
                        <li>• 您的资金受到银行级安全保护</li>
                        <li>• 所有交易都经过加密处理</li>
                        <li>• 24小时风控监测系统</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-indigo-600">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs font-medium">钱包</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs font-medium">VIP</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">推荐</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </div>
</body>
</html>