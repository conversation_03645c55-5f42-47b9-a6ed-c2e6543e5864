<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推荐有礼 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .money-gradient {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
        }
        .invitation-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .level-card {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="money-gradient shadow-lg">
        <div class="flex items-center justify-between p-4 text-white">
            <h1 class="text-xl font-bold flex items-center">
                <i class="fas fa-users mr-2"></i>
                推荐有礼
            </h1>
            <button class="p-2 text-white/70 hover:text-white">
                <i class="fas fa-question-circle text-lg"></i>
            </button>
        </div>
    </div>

    <!-- 收益概览 -->
    <div class="p-6">
        <div class="money-gradient rounded-2xl p-6 text-white mb-6 shadow-xl">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold mb-2">我的推荐收益</h2>
                <p class="opacity-90">邀请好友，轻松赚钱</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-3xl font-bold">¥1,235.60</p>
                    <p class="text-sm opacity-90">累计收益</p>
                </div>
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-3xl font-bold">¥89.50</p>
                    <p class="text-sm opacity-90">今日收益</p>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-xl font-bold">24</p>
                    <p class="text-xs opacity-80">直接下级</p>
                </div>
                <div class="text-center">
                    <p class="text-xl font-bold">156</p>
                    <p class="text-xs opacity-80">团队总数</p>
                </div>
                <div class="text-center">
                    <p class="text-xl font-bold">8</p>
                    <p class="text-xs opacity-80">今日新增</p>
                </div>
            </div>
        </div>

        <!-- 我的邀请码 -->
        <div class="invitation-card rounded-2xl p-6 text-white mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">
                    <i class="fas fa-gift mr-2"></i>我的邀请码
                </h3>
                <button class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full text-sm font-medium transition-colors">
                    <i class="fas fa-copy mr-1"></i>复制
                </button>
            </div>
            
            <div class="bg-white/20 rounded-xl p-4 mb-4">
                <div class="text-center">
                    <p class="text-2xl font-bold tracking-widest mb-2">TK2024ABC</p>
                    <p class="text-sm opacity-90">分享此邀请码给好友注册</p>
                </div>
            </div>

            <div class="flex space-x-3">
                <button class="flex-1 bg-white/20 hover:bg-white/30 py-3 px-4 rounded-xl font-medium transition-colors">
                    <i class="fab fa-weixin mr-2"></i>微信分享
                </button>
                <button class="flex-1 bg-white/20 hover:bg-white/30 py-3 px-4 rounded-xl font-medium transition-colors">
                    <i class="fab fa-qq mr-2"></i>QQ分享
                </button>
                <button class="flex-1 bg-white/20 hover:bg-white/30 py-3 px-4 rounded-xl font-medium transition-colors">
                    <i class="fas fa-link mr-2"></i>复制链接
                </button>
            </div>
        </div>

        <!-- 分销层级说明 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-sitemap text-indigo-500 mr-2"></i>三级分销体系
            </h3>
            
            <div class="space-y-4">
                <!-- 一级分销 -->
                <div class="flex items-center p-4 bg-gradient-to-r from-red-100 to-pink-100 rounded-xl">
                    <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        1
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-red-900">一级下线（直推）</h4>
                        <p class="text-sm text-red-700">好友通过你的邀请码注册</p>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-red-600">30%</p>
                        <p class="text-xs text-red-500">佣金比例</p>
                    </div>
                </div>

                <!-- 二级分销 -->
                <div class="flex items-center p-4 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-xl">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        2
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-blue-900">二级下线</h4>
                        <p class="text-sm text-blue-700">你的下线推荐的用户</p>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-blue-600">15%</p>
                        <p class="text-xs text-blue-500">佣金比例</p>
                    </div>
                </div>

                <!-- 三级分销 -->
                <div class="flex items-center p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                        3
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-green-900">三级下线</h4>
                        <p class="text-sm text-green-700">二级下线推荐的用户</p>
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-green-600">8%</p>
                        <p class="text-xs text-green-500">佣金比例</p>
                    </div>
                </div>
            </div>

            <!-- 计算示例 -->
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <div class="flex items-center mb-3">
                    <i class="fas fa-calculator text-yellow-600 mr-2"></i>
                    <h4 class="font-semibold text-yellow-900">收益计算示例</h4>
                </div>
                <p class="text-sm text-yellow-800 mb-2">
                    如果您的一级下线完成一个10 USDT的任务：
                </p>
                <p class="text-sm text-yellow-800">
                    您可获得：10 × 30% = <span class="font-bold text-yellow-900">3 USDT</span> 佣金
                </p>
            </div>
        </div>

        <!-- 我的团队 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-users text-indigo-500 mr-2"></i>我的团队
                </h3>
                <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    查看全部
                </button>
            </div>

            <div class="space-y-3">
                <!-- 团队成员1 -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b35c?w=40&h=40&fit=crop&crop=face" 
                             alt="用户头像" 
                             class="w-10 h-10 rounded-full mr-3">
                        <div>
                            <p class="font-medium text-gray-900">李小美</p>
                            <p class="text-sm text-gray-500">一级下线 · VIP2</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+¥45.60</p>
                        <p class="text-xs text-gray-500">今日贡献</p>
                    </div>
                </div>

                <!-- 团队成员2 -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                             alt="用户头像" 
                             class="w-10 h-10 rounded-full mr-3">
                        <div>
                            <p class="font-medium text-gray-900">王大强</p>
                            <p class="text-sm text-gray-500">一级下线 · VIP1</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+¥28.30</p>
                        <p class="text-xs text-gray-500">今日贡献</p>
                    </div>
                </div>

                <!-- 团队成员3 -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" 
                             alt="用户头像" 
                             class="w-10 h-10 rounded-full mr-3">
                        <div>
                            <p class="font-medium text-gray-900">张丽丽</p>
                            <p class="text-sm text-gray-500">二级下线 · VIP1</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-600">+¥15.60</p>
                        <p class="text-xs text-gray-500">今日贡献</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推广素材 -->
        <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-images text-indigo-500 mr-2"></i>推广素材
            </h3>
            
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gradient-to-r from-purple-100 to-indigo-100 rounded-xl p-4 text-center">
                    <i class="fas fa-image text-purple-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-purple-900">宣传海报</p>
                    <p class="text-sm text-purple-600">5张高清海报</p>
                    <button class="mt-2 bg-purple-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-600 transition-colors">
                        下载
                    </button>
                </div>
                
                <div class="bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl p-4 text-center">
                    <i class="fas fa-video text-green-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-green-900">推广视频</p>
                    <p class="text-sm text-green-600">30秒宣传片</p>
                    <button class="mt-2 bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 transition-colors">
                        下载
                    </button>
                </div>
                
                <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-xl p-4 text-center">
                    <i class="fas fa-quote-right text-yellow-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-yellow-900">文案模板</p>
                    <p class="text-sm text-yellow-600">朋友圈文案</p>
                    <button class="mt-2 bg-yellow-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-yellow-600 transition-colors">
                        复制
                    </button>
                </div>
                
                <div class="bg-gradient-to-r from-red-100 to-pink-100 rounded-xl p-4 text-center">
                    <i class="fas fa-share-alt text-red-600 text-2xl mb-2"></i>
                    <p class="font-semibold text-red-900">分享链接</p>
                    <p class="text-sm text-red-600">专属推广链接</p>
                    <button class="mt-2 bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600 transition-colors">
                        生成
                    </button>
                </div>
            </div>
        </div>

        <!-- 成功案例 -->
        <div class="level-card rounded-2xl p-6 text-white shadow-lg">
            <div class="text-center mb-4">
                <i class="fas fa-trophy text-3xl mb-3"></i>
                <h3 class="text-xl font-bold">推广达人榜</h3>
                <p class="opacity-90">看看大家的收益</p>
            </div>
            
            <div class="space-y-3">
                <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">1</span>
                        </div>
                        <div>
                            <p class="font-semibold">推广王者</p>
                            <p class="text-sm opacity-80">团队586人</p>
                        </div>
                    </div>
                    <p class="text-xl font-bold">¥15,628</p>
                </div>
                
                <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">2</span>
                        </div>
                        <div>
                            <p class="font-semibold">收益达人</p>
                            <p class="text-sm opacity-80">团队423人</p>
                        </div>
                    </div>
                    <p class="text-xl font-bold">¥12,856</p>
                </div>
                
                <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">3</span>
                        </div>
                        <div>
                            <p class="font-semibold">分享高手</p>
                            <p class="text-sm opacity-80">团队298人</p>
                        </div>
                    </div>
                    <p class="text-xl font-bold">¥9,245</p>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <p class="text-sm opacity-90">你也可以成为下一个推广达人！</p>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs font-medium">钱包</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-crown text-xl mb-1"></i>
                <span class="text-xs font-medium">VIP</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-indigo-600">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">推荐</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-gray-500 hover:text-indigo-600 transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">我的</span>
            </button>
        </div>
    </div>
</body>
</html>