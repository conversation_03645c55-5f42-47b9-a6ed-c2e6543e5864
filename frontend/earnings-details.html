<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收益明细 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding-bottom: 80px;
        }
        .earnings-gradient {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .income-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .expense-card {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .chart-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <div class="earnings-gradient shadow-lg">
        <div class="flex items-center justify-between p-4 text-white">
            <button onclick="window.location.href='index.html#profile'" class="p-2 -ml-2 text-white/70 hover:text-white">
                <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <h1 class="text-xl font-bold">收益明细</h1>
            <button class="p-2 -mr-2 text-white/70 hover:text-white">
                <i class="fas fa-download text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 收益概览 -->
    <div class="p-6">
        <div class="earnings-gradient rounded-2xl p-6 text-white mb-6 shadow-xl">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold mb-2">总收益统计</h2>
                <p class="opacity-90">您的收益详情</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-3xl font-bold">¥856.30</p>
                    <p class="text-sm opacity-90">累计收益</p>
                </div>
                <div class="bg-white/20 rounded-xl p-4 text-center">
                    <p class="text-3xl font-bold">¥68.50</p>
                    <p class="text-sm opacity-90">今日收益</p>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-xl font-bold">¥456.20</p>
                    <p class="text-xs opacity-80">任务收益</p>
                </div>
                <div class="text-center">
                    <p class="text-xl font-bold">¥320.10</p>
                    <p class="text-xs opacity-80">推荐收益</p>
                </div>
                <div class="text-center">
                    <p class="text-xl font-bold">¥80.00</p>
                    <p class="text-xs opacity-80">签到奖励</p>
                </div>
            </div>
        </div>

        <!-- 收益图表 -->
        <div class="chart-container rounded-2xl p-6 text-white mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">7天收益趋势</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-white/20 rounded-full text-sm">7天</button>
                    <button class="px-3 py-1 bg-white/10 rounded-full text-sm">30天</button>
                </div>
            </div>
            
            <!-- 简单的柱状图 -->
            <div class="flex items-end justify-between h-32 mb-4">
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 60%"></div>
                    <span class="text-xs mt-2">周一</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 80%"></div>
                    <span class="text-xs mt-2">周二</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 45%"></div>
                    <span class="text-xs mt-2">周三</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 90%"></div>
                    <span class="text-xs mt-2">周四</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 70%"></div>
                    <span class="text-xs mt-2">周五</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 100%"></div>
                    <span class="text-xs mt-2">周六</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 bg-white/30 rounded-t" style="height: 85%"></div>
                    <span class="text-xs mt-2">周日</span>
                </div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">筛选条件</h3>
                <button class="text-green-600 hover:text-green-800 text-sm font-medium">
                    重置
                </button>
            </div>
            
            <!-- 收益类型筛选 -->
            <div class="flex flex-wrap gap-2 mb-4">
                <button class="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium transition-all">
                    全部
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    任务收益
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    推荐收益
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    签到奖励
                </button>
                <button class="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all">
                    VIP奖励
                </button>
            </div>

            <!-- 时间筛选 -->
            <div class="flex space-x-3">
                <select class="flex-1 px-3 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    <option>今天</option>
                    <option>昨天</option>
                    <option>本周</option>
                    <option>本月</option>
                    <option>自定义</option>
                </select>
                <button class="px-4 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- 收益记录列表 -->
        <div class="space-y-4">
            <!-- 任务收益 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-12 h-12 income-card rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-heart text-white text-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">TikTok点赞任务</h4>
                            <p class="text-sm text-gray-600">任务ID: #TK001</p>
                            <p class="text-xs text-gray-500">2024-01-15 14:32</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-green-600">+2.50 USDT</p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            任务收益
                        </span>
                    </div>
                </div>
                
                <!-- 收益详情 -->
                <div class="bg-green-50 rounded-xl p-3">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">基础奖励:</span>
                            <span class="font-medium">2.00 USDT</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">VIP加成:</span>
                            <span class="font-medium text-green-600">+0.50 USDT</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐收益 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-users text-white text-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">推荐佣金</h4>
                            <p class="text-sm text-gray-600">来自: 李小美</p>
                            <p class="text-xs text-gray-500">2024-01-15 16:20</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-purple-600">+0.75 USDT</p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            一级推荐
                        </span>
                    </div>
                </div>
                
                <!-- 推荐详情 -->
                <div class="bg-purple-50 rounded-xl p-3">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">任务收益:</span>
                            <span class="font-medium">2.50 USDT</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">佣金比例:</span>
                            <span class="font-medium text-purple-600">30%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 签到奖励 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-white text-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">每日签到奖励</h4>
                            <p class="text-sm text-gray-600">连续签到第4天</p>
                            <p class="text-xs text-gray-500">2024-01-15 08:30</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-yellow-600">+1.00 USDT</p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            签到奖励
                        </span>
                    </div>
                </div>
            </div>

            <!-- VIP升级奖励 -->
            <div class="bg-white rounded-2xl p-4 shadow-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center mr-3">
                            <i class="fas fa-crown text-white text-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">VIP升级奖励</h4>
                            <p class="text-sm text-gray-600">升级至VIP1</p>
                            <p class="text-xs text-gray-500">2024-01-10 12:00</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-yellow-600">+5.00 USDT</p>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            VIP奖励
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载更多 -->
        <div class="text-center mt-6">
            <button class="bg-white text-gray-600 px-6 py-3 rounded-xl shadow-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-down mr-2"></i>加载更多
            </button>
        </div>
    </div>
</body>
</html>
