<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
            /* 禁用双击缩放和触摸缩放 */
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* 禁用所有元素的双击缩放 */
        * {
            touch-action: manipulation;
        }

        /* 移动端容器 */
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        .page {
            display: none;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        .page.active {
            display: block;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background: white;
            border-top: 1px solid #e5e7eb;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            z-index: 1000;
            max-width: 100vw;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            transition: all 0.3s ease;
            flex: 1;
        }

        .nav-item.active {
            color: #6366f1;
        }

        .nav-item:not(.active) {
            color: #9ca3af;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .user-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .task-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .checkin-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }

        .checkin-day {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            position: relative;
        }

        .checkin-day.completed {
            background: #10b981;
        }

        .checkin-day.current {
            background: #3b82f6;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        }

        .checkin-day.pending {
            background: #d1d5db;
            color: #6b7280;
        }

        .checkin-day.special {
            background: #f59e0b;
        }

        /* 轮播图样式 */
        .banner-container {
            height: 160px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.25);
        }

        .banner-slider {
            height: 100%;
        }

        .banner-slide {
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .banner-slide::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            z-index: 1;
        }

        .banner-slide::after {
            content: '';
            position: absolute;
            bottom: -30%;
            right: -10%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            z-index: 1;
        }

        .slide-content {
            position: relative;
            z-index: 2;
            width: 100%;
        }

        .slide-icon {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .slide-icon:hover {
            background: rgba(255, 255, 255, 0.25) !important;
            transform: scale(1.05);
        }

        .banner-dot {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .banner-dot.active {
            background-color: rgba(255, 255, 255, 0.95) !important;
            transform: scale(1.3);
            width: 20px !important;
            border-radius: 10px !important;
            box-shadow: 0 2px 12px rgba(255, 255, 255, 0.4);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .mobile-container {
                width: 100%;
            }

            .bottom-nav {
                width: 100%;
            }

            .banner-container {
                height: 120px;
            }
        }

        @media (max-width: 480px) {
            .px-6 {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .banner-container {
                height: 110px;
            }
        }
    </style>
</head>

<body>
    <!-- 移动端容器 -->
    <div class="mobile-container">

        <!-- 首页 -->
        <div class="page active" id="home-page">
            <!-- 顶部导航 -->
            <div class="gradient-bg px-6 pt-6 pb-4">
                <div class="flex items-center text-white">
                    <div class="flex items-center">
                        <div class="bg-white/20 w-12 h-12 rounded-full flex items-center justify-center mr-3">
                            <i class="fab fa-tiktok text-2xl"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold">TikTok任务平台</h1>
                            <p class="text-sm opacity-90">点赞赚钱，轻松收益</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 轮播图 -->
            <div class="px-6 pb-2 gradient-bg">
                <div class="banner-container relative rounded-3xl overflow-hidden">
                    <div class="banner-slider flex transition-transform duration-700 ease-out" id="bannerSlider">
                        <!-- 轮播图1 - 推荐有奖 -->
                        <div class="banner-slide min-w-full bg-gradient-to-br from-purple-500 via-indigo-500 to-blue-500 p-6 text-white relative">
                            <div class="slide-content flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-trophy text-yellow-900 text-sm"></i>
                                        </div>
                                        <h3 class="text-lg font-bold" >推荐有奖</h3>
                                    </div>
                                    <p class="text-sm opacity-95 mb-4 leading-relaxed" >邀请好友获得丰厚奖励，三级分销收益</p>
                                    <button class="bg-white/20 backdrop-blur-sm text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-white/30 transition-all border border-white/30" onclick="showPage('referral')">
                                        <span >邀请好友</span>
                                    </button>
                                </div>
                                <div class="slide-icon w-16 h-16 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-users text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 轮播图2 - VIP升级 -->
                        <div class="banner-slide min-w-full bg-gradient-to-br from-orange-400 via-pink-500 to-red-500 p-6 text-white relative">
                            <div class="slide-content flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-yellow-300 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-crown text-yellow-900 text-sm"></i>
                                        </div>
                                        <h3 class="text-lg font-bold">VIP升级优惠</h3>
                                    </div>
                                    <p class="text-sm opacity-95 mb-4 leading-relaxed">升级VIP享受更多特权，任务收益翻倍</p>
                                    <button class="bg-white/20 backdrop-blur-sm text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-white/30 transition-all border border-white/30" onclick="showPage('vip')">
                                        了解详情
                                    </button>
                                </div>
                                <div class="slide-icon w-16 h-16 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-crown text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 轮播图3 - 新用户福利 -->
                        <div class="banner-slide min-w-full bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-500 p-6 text-white relative">
                            <div class="slide-content flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-green-300 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-gift text-green-900 text-sm"></i>
                                        </div>
                                        <h3 class="text-lg font-bold">新用户福利</h3>
                                    </div>
                                    <p class="text-sm opacity-95 mb-4 leading-relaxed">注册即送10USDT，完成首个任务再得5USDT</p>
                                    <button class="bg-white/20 backdrop-blur-sm text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-white/30 transition-all border border-white/30">
                                        立即领取
                                    </button>
                                </div>
                                <div class="slide-icon w-16 h-16 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-gift text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 轮播指示器 -->
                    <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        <button class="banner-dot w-2 h-2 rounded-full bg-white/60 active" onclick="goToSlide(0)"></button>
                        <button class="banner-dot w-2 h-2 rounded-full bg-white/60" onclick="goToSlide(1)"></button>
                        <button class="banner-dot w-2 h-2 rounded-full bg-white/60" onclick="goToSlide(2)"></button>
                    </div>
                </div>
            </div>

            <!-- 简化的用户余额卡片 -->
            <div class="px-6 mt-4 relative z-10">
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop&crop=face"
                                alt="用户头像" class="w-12 h-12 rounded-full border-2 border-gray-200 mr-3">
                            <div>
                                <h2 class="text-lg font-bold text-gray-900">张小明</h2>
                                <div class="flex items-center">
                                    <div class="bg-yellow-500 px-2 py-1 rounded-full mr-2">
                                        <span class="text-white text-xs font-bold vip-badge">VIP 1</span>
                                    </div>
                                    <span class="text-gray-500 text-sm">ID: 1234567</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-indigo-600 balance-amount">128.56</p>
                            <p class="text-sm text-gray-500">USDT余额</p>
                            <p class="text-green-600 text-sm font-medium">今日 +15.75</p>
                        </div>
                    </div>
                </div>

                <!-- 核心功能入口 -->
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-rocket text-indigo-500 mr-2"></i>快速开始
                    </h3>
                    <div class="grid grid-cols-4 gap-3">
                        <button
                            class="flex flex-col items-center p-3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl hover:from-blue-100 hover:to-indigo-100 transition-all"
                            onclick="showPage('tasks')">
                            <i class="fas fa-tasks text-blue-600 text-xl mb-2"></i>
                            <span class="text-xs font-medium text-blue-800">任务大厅</span>
                            <span class="text-xs text-blue-600 mt-1 available-tasks-count">156个任务</span>
                        </button>
                        <button
                            class="flex flex-col items-center p-3 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl hover:from-purple-100 hover:to-indigo-100 transition-all"
                            onclick="window.location.href='daily-checkin.html'">
                            <i class="fas fa-crown text-purple-600 text-xl mb-2"></i>
                            <span class="text-xs font-medium text-purple-800">每日签到</span>
                            <span class="text-xs text-purple-600 mt-1">+18积分</span>
                        </button>
                        <button
                            class="flex flex-col items-center p-3 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl hover:from-yellow-100 hover:to-orange-100 transition-all border border-yellow-200"
                            onclick="showPage('vip')">
                            <i class="fas fa-crown text-yellow-600 text-xl mb-2"></i>
                            <span class="text-xs font-medium text-yellow-800">VIP特权</span>
                            <span class="text-xs text-yellow-600 mt-1">升级享特权</span>
                        </button>
                        <button
                            class="flex flex-col items-center p-3 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl hover:from-purple-100 hover:to-pink-100 transition-all"
                            onclick="showPage('wallet')">
                            <i class="fas fa-wallet text-purple-600 text-xl mb-2"></i>
                            <span class="text-xs font-medium text-purple-800">我的钱包</span>
                            <span class="text-xs text-purple-600 mt-1">充值提现</span>
                        </button>
                    </div>
                </div>

                <!-- 热门任务推荐 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-fire text-orange-500 mr-2"></i>热门任务
                        </h3>
                        <button class="text-indigo-600 text-sm font-medium" onclick="showPage('tasks')">查看全部</button>
                    </div>

                    <!-- 热门任务列表 -->
                    <div class="space-y-3">
                        <div class="bg-gradient-to-r from-red-500 to-pink-500 rounded-xl p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fab fa-tiktok text-2xl mr-3"></i>
                                    <div>
                                        <h4 class="font-semibold">TikTok点赞任务</h4>
                                        <p class="text-sm opacity-90">为指定视频点赞</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xl font-bold">$2.50</p>
                                    <button
                                        class="mt-1 bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-sm font-medium transition-colors"
                                        onclick="goToTaskDetails('like', '2.50', 'TikTok点赞任务', '为指定视频点赞')">
                                        开始
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-user-plus text-2xl mr-3"></i>
                                    <div>
                                        <h4 class="font-semibold">TikTok关注任务</h4>
                                        <p class="text-sm opacity-90">关注指定用户账号</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xl font-bold">$5.00</p>
                                    <button
                                        class="mt-1 bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-sm font-medium transition-colors"
                                        onclick="goToTaskDetails('follow', '5.00', 'TikTok关注任务', '关注指定用户账号')">
                                        开始
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-comment text-2xl mr-3"></i>
                                    <div>
                                        <h4 class="font-semibold">TikTok评论任务</h4>
                                        <p class="text-sm opacity-90">发表正面评论</p>
                                        <span
                                            class="bg-white/30 text-xs px-2 py-1 rounded-full mt-1 inline-block">VIP专享</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xl font-bold">$8.00</p>
                                    <button
                                        class="mt-1 bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-sm font-medium transition-colors"
                                        onclick="goToTaskDetails('comment', '8.00', 'TikTok评论任务', '发表正面评论')">
                                        开始
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务大厅页面 -->
        <div class="page" id="tasks-page">
            <div class="gradient-bg px-6 py-6">
                <div class="flex items-center text-white">
                    <h1 class="text-xl font-bold">任务大厅</h1>
                </div>
            </div>

            <div class="px-6 py-6">
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold text-indigo-600 available-tasks-number">156</p>
                            <p class="text-sm text-gray-500">可用任务</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-green-600">$68.50</p>
                            <p class="text-sm text-gray-500">今日收入</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-blue-600">12</p>
                            <p class="text-sm text-gray-500">完成任务</p>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="space-y-4" id="task-list">
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-heart text-white"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">TikTok点赞任务</h3>
                                    <p class="text-sm text-gray-600">为指定视频点赞</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-red-600">$2.50</p>
                                <button class="mt-2 bg-red-500 text-white px-4 py-1 rounded-full text-sm"
                                        onclick="goToTaskDetails('like', '2.50', 'TikTok点赞任务', '为指定视频点赞')">
                                    开始
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user-plus text-white"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">TikTok关注任务</h3>
                                    <p class="text-sm text-gray-600">关注指定用户账号</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-600">$5.00</p>
                                <button class="mt-2 bg-blue-500 text-white px-4 py-1 rounded-full text-sm"
                                        onclick="goToTaskDetails('follow', '5.00', 'TikTok关注任务', '关注指定用户账号')">
                                    开始
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-comment text-white"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">TikTok评论任务</h3>
                                    <p class="text-sm text-gray-600">发表正面评论</p>
                                    <span
                                        class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">VIP专享</span>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-yellow-600">$8.00</p>
                                <button class="mt-2 bg-yellow-500 text-white px-4 py-1 rounded-full text-sm"
                                        onclick="goToTaskDetails('comment', '8.00', 'TikTok评论任务', '发表正面评论')">
                                    开始
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 每日签到页面 -->
        <div class="page" id="daily-checkin-page">
            <div class="gradient-bg px-6 py-6">
                <div class="flex items-center text-white">
                    <button onclick="showPage('home')" class="p-2 -ml-2 mr-3">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-xl font-bold">每日签到</h1>
                </div>
            </div>

            <div class="px-6 py-6">
                <!-- 签到状态 -->
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <div class="text-center mb-6">
                        <div
                            class="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-check text-white text-3xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">连续签到第 4 天</h2>
                        <p class="text-gray-600">坚持签到，获得VIP积分奖励</p>
                    </div>

                    <!-- 签到进度 -->
                    <div class="checkin-progress">
                        <div class="checkin-day completed">
                            <i class="fas fa-check"></i>
                            <span class="absolute -bottom-6 text-xs text-gray-600">+10</span>
                        </div>
                        <div class="checkin-day completed">
                            <i class="fas fa-check"></i>
                            <span class="absolute -bottom-6 text-xs text-gray-600">+12</span>
                        </div>
                        <div class="checkin-day completed">
                            <i class="fas fa-check"></i>
                            <span class="absolute -bottom-6 text-xs text-gray-600">+15</span>
                        </div>
                        <div class="checkin-day current">
                            <i class="fas fa-star"></i>
                            <span class="absolute -bottom-6 text-xs text-blue-600">+18</span>
                        </div>
                        <div class="checkin-day pending">
                            5
                            <span class="absolute -bottom-6 text-xs text-gray-600">+20</span>
                        </div>
                        <div class="checkin-day pending">
                            6
                            <span class="absolute -bottom-6 text-xs text-gray-600">+25</span>
                        </div>
                        <div class="checkin-day special">
                            <i class="fas fa-gift"></i>
                            <span class="absolute -bottom-6 text-xs text-orange-600">+30</span>
                        </div>
                    </div>

                    <div class="mt-12">
                        <button
                            class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold py-4 px-6 rounded-xl shadow-lg">
                            <i class="fas fa-calendar-check mr-2"></i>
                            立即签到 +18VIP积分
                        </button>
                    </div>
                </div>

                <!-- 签到统计 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">签到统计</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-indigo-600">4</p>
                            <p class="text-sm text-gray-500">连续天数</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-600">156</p>
                            <p class="text-sm text-gray-500">累计天数</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-orange-600">$428.50</p>
                            <p class="text-sm text-gray-500">签到收益</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 钱包页面 -->
        <div class="page" id="wallet-page">
            <div class="gradient-bg px-6 py-6">
                <div class="flex items-center justify-between text-white">
                    <h1 class="text-xl font-bold">我的钱包</h1>
                    <div class="flex items-center space-x-3">
                        <button class="p-2 text-white/80 hover:text-white">
                            <i class="fas fa-history"></i>
                        </button>
                        <button class="p-2 text-white/80 hover:text-white">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="px-6 py-6">
                <!-- 余额卡片 -->
                <div class="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl p-6 text-white mb-6 shadow-lg">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <p class="text-white/80 text-sm">总余额</p>
                            <div class="flex items-center mt-1">
                                <div
                                    class="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">₮</span>
                                </div>
                                <span class="text-3xl font-bold balance-amount">128.56</span>
                                <span class="text-lg ml-2 opacity-90">USDT</span>
                            </div>
                        </div>
                        <button class="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors">
                            <i class="fas fa-eye text-xl"></i>
                        </button>
                    </div>

                    <!-- 今日收益 -->
                    <div class="bg-white/20 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white/80 text-sm">今日收益</p>
                                <p class="text-xl font-semibold text-green-300">+15.75 USDT</p>
                            </div>
                            <div class="text-right">
                                <p class="text-white/80 text-sm">昨日收益</p>
                                <p class="text-lg font-medium">+12.30 USDT</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button
                        class="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:from-green-600 hover:to-emerald-600 transition-all"
                        onclick="window.location.href='deposit.html'">
                        <i class="fas fa-plus-circle mb-2 text-xl"></i>
                        <p>充值</p>
                    </button>
                    <button
                        class="bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:from-blue-600 hover:to-indigo-600 transition-all"
                        onclick="window.location.href='withdraw.html'">
                        <i class="fas fa-arrow-up mb-2 text-xl"></i>
                        <p>提现</p>
                    </button>
                </div>

                <!-- 统计信息 -->
                <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-chart-line text-indigo-500 mr-2"></i>收益统计
                    </h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-600">$2,456</p>
                            <p class="text-sm text-gray-500">本月收益</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-blue-600">$8,923</p>
                            <p class="text-sm text-gray-500">累计收益</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-purple-600">156</p>
                            <p class="text-sm text-gray-500">完成任务</p>
                        </div>
                    </div>
                </div>

                <!-- 交易记录 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-receipt text-indigo-500 mr-2"></i>交易记录
                        </h3>
                        <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                    </div>

                    <!-- 交易记录列表 -->
                    <div class="space-y-4">
                        <!-- 任务收益 -->
                        <div class="flex items-center justify-between p-4 bg-green-50 rounded-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-plus text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">任务收益</p>
                                    <p class="text-sm text-gray-500">TikTok点赞任务</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-600">+2.50 USDT</p>
                                <p class="text-xs text-gray-500">刚刚</p>
                            </div>
                        </div>

                        <!-- 充值记录 -->
                        <div class="flex items-center justify-between p-4 bg-blue-50 rounded-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-arrow-down text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">USDT充值</p>
                                    <p class="text-sm text-gray-500">钱包充值</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-blue-600">+100.00 USDT</p>
                                <p class="text-xs text-gray-500">2小时前</p>
                            </div>
                        </div>

                        <!-- 推荐奖励 -->
                        <div class="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-gift text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">推荐奖励</p>
                                    <p class="text-sm text-gray-500">三级分销奖励</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-purple-600">+5.20 USDT</p>
                                <p class="text-xs text-gray-500">2天前</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全提示 -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <i class="fas fa-shield-alt text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-blue-900 mb-1">
                                <i class="fas fa-lock mr-1"></i>资金安全
                            </h4>
                            <ul class="text-blue-800 text-sm space-y-1">
                                <li>• 您的资金受到银行级安全保护</li>
                                <li>• 所有交易都经过加密处理</li>
                                <li>• 24小时风控监测系统</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page" id="vip-page">
            <div class="bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 px-6 py-6">
                <div class="flex items-center text-black">
                    <button onclick="showPage('home')" class="p-2 -ml-2 mr-3">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-xl font-bold flex items-center">
                        <i class="fas fa-crown mr-2 text-yellow-800"></i>
                        VIP特权中心
                    </h1>
                    <button class="p-2 text-black/70 hover:text-black ml-auto">
                        <i class="fas fa-question-circle text-lg"></i>
                    </button>
                </div>
            </div>

            <div class="px-6 py-6">
                <!-- 当前VIP状态 -->
                <div
                    class="bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-600 rounded-2xl p-6 text-white mb-6 shadow-xl relative overflow-hidden">
                    <div class="relative">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div
                                    class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-crown text-2xl text-white"></i>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold current-vip-level">VIP 1</h2>
                                    <p class="opacity-90 current-vip-subtitle">青铜会员</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-semibold">距离升级</p>
                                <p class="text-sm opacity-80">还需 15 天</p>
                            </div>
                        </div>

                        <!-- VIP进度条 -->
                        <div class="bg-white/20 rounded-full p-1 mb-4">
                            <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full h-3"
                                style="width: 75%"></div>
                        </div>
                        <p class="text-center text-sm opacity-90">当前经验值：750/1000</p>
                    </div>
                </div>

                <!-- 我的特权 -->
                <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-gem text-purple-500 mr-2"></i>我的特权
                    </h3>
                    <div class="grid grid-cols-2 gap-4" id="vip-privileges">
                        <div class="bg-gradient-to-r from-green-100 to-emerald-100 p-4 rounded-xl">
                            <i class="fas fa-tasks text-green-600 text-2xl mb-2"></i>
                            <p class="font-semibold text-green-800">每日任务</p>
                            <p class="text-sm text-green-600 daily-tasks">15个/天</p>
                        </div>
                        <div class="bg-gradient-to-r from-blue-100 to-indigo-100 p-4 rounded-xl">
                            <i class="fas fa-coins text-blue-600 text-2xl mb-2"></i>
                            <p class="font-semibold text-blue-800">收益加成</p>
                            <p class="text-sm text-blue-600 income-bonus">+10%</p>
                        </div>
                        <div class="bg-gradient-to-r from-purple-100 to-violet-100 p-4 rounded-xl">
                            <i class="fas fa-headset text-purple-600 text-2xl mb-2"></i>
                            <p class="font-semibold text-purple-800">专属客服</p>
                            <p class="text-sm text-purple-600 customer-service">优先处理</p>
                        </div>
                        <div class="bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-xl">
                            <i class="fas fa-gift text-yellow-600 text-2xl mb-2"></i>
                            <p class="font-semibold text-yellow-800">生日礼品</p>
                            <p class="text-sm text-yellow-600 birthday-gift">专属奖励</p>
                        </div>
                    </div>
                </div>

                <!-- VIP等级对比 -->
                <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-star text-yellow-500 mr-2"></i>VIP等级对比
                    </h3>

                    <div class="space-y-4">
                        <!-- VIP 1 (当前) -->
                        <div class="vip-comparison-card border-2 border-indigo-500 bg-indigo-50 rounded-xl p-4" data-vip-level="1">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-crown text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-indigo-900">VIP 1 - 青铜</h4>
                                        <p class="text-sm text-indigo-700">当前等级</p>
                                    </div>
                                </div>
                                <span
                                    class="vip-status-badge bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">当前</span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">15个</p>
                                    <p class="text-gray-600">每日任务</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">+10%</p>
                                    <p class="text-gray-600">收益加成</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">优先</p>
                                    <p class="text-gray-600">客服支持</p>
                                </div>
                            </div>
                        </div>

                        <!-- VIP 2 -->
                        <div class="vip-comparison-card border border-gray-200 rounded-xl p-4" data-vip-level="2">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-crown text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-purple-900">VIP 2 - 白银</h4>
                                        <p class="text-sm text-purple-700">30 USDT</p>
                                    </div>
                                </div>
                                <button
                                    class="vip-status-badge bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-purple-600 transition-colors" onclick="upgradeVip(2, 30)">升级</button>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">25个</p>
                                    <p class="text-gray-600">每日任务</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">+20%</p>
                                    <p class="text-gray-600">收益加成</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">专属</p>
                                    <p class="text-gray-600">任务池</p>
                                </div>
                            </div>
                        </div>

                        <!-- VIP 3 -->
                        <div class="vip-comparison-card border border-gray-200 rounded-xl p-4" data-vip-level="3">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-crown text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-yellow-900">VIP 3 - 黄金</h4>
                                        <p class="text-sm text-yellow-700">68 USDT</p>
                                    </div>
                                </div>
                                <button
                                    class="vip-status-badge bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-yellow-500 hover:to-yellow-700 transition-all" onclick="upgradeVip(3, 68)">升级</button>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">40个</p>
                                    <p class="text-gray-600">每日任务</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">+35%</p>
                                    <p class="text-gray-600">收益加成</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">1v1</p>
                                    <p class="text-gray-600">专属客服</p>
                                </div>
                            </div>
                        </div>

                        <!-- VIP 4 -->
                        <div class="vip-comparison-card border border-gray-200 rounded-xl p-4 relative" data-vip-level="4">
                            <div
                                class="absolute top-0 right-0 bg-red-500 text-white px-2 py-1 text-xs font-bold rounded-bl-lg">
                                推荐</div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-crown text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-bold text-red-900">VIP 4 - 钻石</h4>
                                        <p class="text-sm text-red-700">128 USDT</p>
                                    </div>
                                </div>
                                <button
                                    class="vip-status-badge bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:from-red-600 hover:to-pink-600 transition-all" onclick="upgradeVip(4, 128)">升级</button>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">60个</p>
                                    <p class="text-gray-600">每日任务</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">+50%</p>
                                    <p class="text-gray-600">收益加成</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold text-gray-900">专属</p>
                                    <p class="text-gray-600">高价任务</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="page" id="referral-page">
            <div class="bg-gradient-to-r from-green-500 via-emerald-500 to-teal-600 px-6 py-6">
                <div class="flex items-center text-white">
                    <button onclick="showPage('home')" class="p-2 -ml-2 mr-3">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-xl font-bold flex items-center">
                        <i class="fas fa-users mr-2"></i>
                        推荐有礼
                    </h1>
                    <button class="p-2 text-white/70 hover:text-white ml-auto">
                        <i class="fas fa-question-circle text-lg"></i>
                    </button>
                </div>
            </div>

            <div class="px-6 py-6">
                <!-- 收益概览 -->
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-6 text-white mb-6 shadow-xl">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold mb-2">我的推荐收益</h2>
                        <p class="opacity-90">邀请好友，轻松赚钱</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-white/20 rounded-xl p-4 text-center">
                            <p class="text-3xl font-bold">$1,235.60</p>
                            <p class="text-sm opacity-90">累计收益</p>
                        </div>
                        <div class="bg-white/20 rounded-xl p-4 text-center">
                            <p class="text-3xl font-bold">$89.50</p>
                            <p class="text-sm opacity-90">今日收益</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-xl font-bold">24</p>
                            <p class="text-xs opacity-80">直接下级</p>
                        </div>
                        <div class="text-center">
                            <p class="text-xl font-bold">156</p>
                            <p class="text-xs opacity-80">团队总数</p>
                        </div>
                        <div class="text-center">
                            <p class="text-xl font-bold">8</p>
                            <p class="text-xs opacity-80">今日新增</p>
                        </div>
                    </div>
                </div>

                <!-- 我的邀请码 -->
                <div class="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl p-6 text-white mb-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">
                            <i class="fas fa-gift mr-2"></i>我的邀请码
                        </h3>
                        <button
                            class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full text-sm font-medium transition-colors">
                            <i class="fas fa-copy mr-1"></i>复制
                        </button>
                    </div>

                    <div class="bg-white/20 rounded-xl p-4 mb-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold tracking-widest mb-2">TK2024ABC</p>
                            <p class="text-sm opacity-90">分享此邀请码给好友注册</p>
                        </div>
                    </div>

                    <div class="flex space-x-4 justify-center">
                        <button
                            class="bg-white/20 hover:bg-white/30 py-3 px-6 rounded-xl font-medium transition-colors" onclick="copyInviteLink()">
                            <i class="fas fa-link mr-2"></i>复制链接
                        </button>
                        <button
                            class="bg-white/20 hover:bg-white/30 py-3 px-6 rounded-xl font-medium transition-colors" onclick="showQRCode()">
                            <i class="fas fa-qrcode mr-2"></i>扫码分享
                        </button>
                    </div>
                </div>

                <!-- 分销层级说明 -->
                <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-sitemap text-indigo-500 mr-2"></i>三级分销体系
                    </h3>

                    <div class="space-y-4">
                        <!-- 一级分销 -->
                        <div class="flex items-center p-4 bg-gradient-to-r from-red-100 to-pink-100 rounded-xl">
                            <div
                                class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                                1
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-red-900">一级下线（直推）</h4>
                                <p class="text-sm text-red-700">好友通过你的邀请码注册</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-red-600">30%</p>
                                <p class="text-xs text-red-500">佣金比例</p>
                            </div>
                        </div>

                        <!-- 二级分销 -->
                        <div class="flex items-center p-4 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-xl">
                            <div
                                class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                                2
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-blue-900">二级下线</h4>
                                <p class="text-sm text-blue-700">你的下线推荐的用户</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-blue-600">15%</p>
                                <p class="text-xs text-blue-500">佣金比例</p>
                            </div>
                        </div>

                        <!-- 三级分销 -->
                        <div class="flex items-center p-4 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl">
                            <div
                                class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                                3
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-green-900">三级下线</h4>
                                <p class="text-sm text-green-700">二级下线推荐的用户</p>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-green-600">8%</p>
                                <p class="text-xs text-green-500">佣金比例</p>
                            </div>
                        </div>
                    </div>

                    <!-- 计算示例 -->
                    <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-calculator text-yellow-600 mr-2"></i>
                            <h4 class="font-semibold text-yellow-900">收益计算示例</h4>
                        </div>
                        <p class="text-sm text-yellow-800 mb-2">
                            如果您的一级下线完成一个10 USDT的任务：
                        </p>
                        <p class="text-sm text-yellow-800">
                            您可获得：10 × 30% = <span class="font-bold text-yellow-900">3 USDT</span> 佣金
                        </p>
                    </div>
                </div>

                <!-- 我的团队 -->
                <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-users text-indigo-500 mr-2"></i>我的团队
                        </h3>
                        <button class="text-indigo-600 text-sm font-medium">查看全部</button>
                    </div>

                    <div class="space-y-3">
                        <!-- 团队成员1 -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b35c?w=40&h=40&fit=crop&crop=face"
                                    alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <p class="font-medium text-gray-900">李小美</p>
                                    <p class="text-sm text-gray-500">一级下线 · VIP2</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-600">+$45.60</p>
                                <p class="text-xs text-gray-500">今日贡献</p>
                            </div>
                        </div>

                        <!-- 团队成员2 -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face"
                                    alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <p class="font-medium text-gray-900">王大强</p>
                                    <p class="text-sm text-gray-500">一级下线 · VIP1</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-600">+$28.30</p>
                                <p class="text-xs text-gray-500">今日贡献</p>
                            </div>
                        </div>

                        <!-- 团队成员3 -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face"
                                    alt="用户头像" class="w-10 h-10 rounded-full mr-3">
                                <div>
                                    <p class="font-medium text-gray-900">张丽丽</p>
                                    <p class="text-sm text-gray-500">二级下线 · VIP1</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-600">+$15.60</p>
                                <p class="text-xs text-gray-500">今日贡献</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推广达人榜 -->
                <div
                    class="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl p-6 text-white shadow-lg">
                    <div class="text-center mb-4">
                        <i class="fas fa-trophy text-3xl mb-3"></i>
                        <h3 class="text-xl font-bold">推广达人榜</h3>
                        <p class="opacity-90">看看大家的收益</p>
                    </div>

                    <div class="space-y-3">
                        <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">1</span>
                                </div>
                                <div>
                                    <p class="font-semibold">推广王者</p>
                                    <p class="text-sm opacity-80">团队586人</p>
                                </div>
                            </div>
                            <p class="text-xl font-bold">$15,628</p>
                        </div>

                        <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">2</span>
                                </div>
                                <div>
                                    <p class="font-semibold">收益达人</p>
                                    <p class="text-sm opacity-80">团队423人</p>
                                </div>
                            </div>
                            <p class="text-xl font-bold">$12,856</p>
                        </div>

                        <div class="bg-white/20 rounded-xl p-3 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm">3</span>
                                </div>
                                <div>
                                    <p class="font-semibold">分享高手</p>
                                    <p class="text-sm opacity-80">团队298人</p>
                                </div>
                            </div>
                            <p class="text-xl font-bold">$9,245</p>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        <p class="text-sm opacity-90">你也可以成为下一个推广达人！</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="page" id="profile-page">
            <div class="gradient-bg px-6 pt-6 pb-8">
                <div class="flex items-center justify-between text-white mb-6">
                    <h1 class="text-xl font-bold">个人中心</h1>
                    <button class="p-2 text-white/70 hover:text-white">
                        <i class="fas fa-cog text-xl"></i>
                    </button>
                </div>

                <div class="flex items-center">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face"
                            alt="用户头像" class="w-20 h-20 rounded-full border-4 border-white/30">
                        <button
                            class="absolute -bottom-1 -right-1 w-7 h-7 bg-white text-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-camera text-sm"></i>
                        </button>
                    </div>
                    <div class="ml-4 flex-1">
                        <h2 class="text-2xl font-bold text-white">张小明</h2>
                        <p class="text-white/80 text-sm mb-2">ID: 1234567</p>
                        <div class="flex items-center">
                            <div
                                class="bg-gradient-to-r from-yellow-400 to-yellow-600 px-3 py-1 rounded-full flex items-center mr-3">
                                <i class="fas fa-crown text-white text-xs mr-1"></i>
                                <span class="text-white text-sm font-medium vip-badge">VIP 1</span>
                            </div>
                            <span class="text-white/80 text-sm">注册于 2024.01.15</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-6 -mt-4 relative z-10">
                <!-- 统计数据 -->
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-indigo-600">156</p>
                            <p class="text-sm text-gray-500">完成任务</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-600">$856.30</p>
                            <p class="text-sm text-gray-500">总收益</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-purple-600">24</p>
                            <p class="text-sm text-gray-500">推荐人数</p>
                        </div>
                    </div>
                </div>

                <!-- 快捷功能 -->
                <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-bolt text-yellow-500 mr-2"></i>快捷功能
                    </h3>
                    <div class="grid grid-cols-4 gap-4">
                        <button class="flex flex-col items-center p-3 bg-purple-50 rounded-xl"
                            onclick="window.location.href='daily-checkin.html'">
                            <i class="fas fa-crown text-purple-600 text-xl mb-2"></i>
                            <span class="text-sm font-medium text-purple-800">签到</span>
                        </button>
                        <button class="flex flex-col items-center p-3 bg-green-50 rounded-xl">
                            <i class="fas fa-gift text-green-600 text-xl mb-2"></i>
                            <span class="text-sm font-medium text-green-800">奖励</span>
                        </button>
                        <button class="flex flex-col items-center p-3 bg-purple-50 rounded-xl"
                            onclick="showPage('referral')">
                            <i class="fas fa-users text-purple-600 text-xl mb-2"></i>
                            <span class="text-sm font-medium text-purple-800">邀请</span>
                        </button>
                        <button class="flex flex-col items-center p-3 bg-orange-50 rounded-xl">
                            <i class="fas fa-headset text-orange-600 text-xl mb-2"></i>
                            <span class="text-sm font-medium text-orange-800">客服</span>
                        </button>
                    </div>
                </div>

                <!-- 账户管理 -->
                <div class="bg-white rounded-2xl shadow-lg mb-6">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-user-circle text-indigo-500 mr-2"></i>账户管理
                        </h3>
                    </div>
                    <div class="divide-y divide-gray-100">
                        <button onclick="openEditProfile()" class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-edit text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">编辑资料</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        <button onclick="openSecuritySettings()" class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">安全设置</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        <button onclick="openChangePassword()" class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-key text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">修改密码</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        <button onclick="openNotifications()" class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-bell text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">消息通知</span>
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">3</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="bg-white rounded-2xl shadow-lg mb-6">
                    <div class="p-6 border-b border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-chart-line text-green-500 mr-2"></i>数据统计
                        </h3>
                    </div>
                    <div class="divide-y divide-gray-100">
                        <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='task-records.html'">
                            <div class="flex items-center">
                                <i class="fas fa-tasks text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">任务记录</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='earnings-details.html'">
                            <div class="flex items-center">
                                <i class="fas fa-coins text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">收益明细</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors" onclick="window.location.href='referral-details.html'">
                            <div class="flex items-center">
                                <i class="fas fa-users text-gray-400 w-5 mr-3"></i>
                                <span class="text-gray-900">推荐明细</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                    </div>
                </div>


                <!-- 退出登录 -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <button
                        class="w-full flex items-center justify-center p-4 text-red-600 hover:bg-red-50 transition-colors rounded-2xl"
                        onclick="logout()">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        <span class="font-medium">退出登录</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('home')">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">首页</span>
            </div>
            <div class="nav-item" onclick="showPage('tasks')">
                <i class="fas fa-tasks text-xl mb-1"></i>
                <span class="text-xs">任务</span>
            </div>
            <div class="nav-item" onclick="showPage('wallet')">
                <i class="fas fa-wallet text-xl mb-1"></i>
                <span class="text-xs">钱包</span>
            </div>
            <div class="nav-item" onclick="showPage('profile')">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">我的</span>
            </div>
        </div>

        <script>
            // 页面切换功能
            function showPage(pageId) {
                // 隐藏所有页面
                const pages = document.querySelectorAll('.page');
                pages.forEach(page => page.classList.remove('active'));

                // 显示目标页面
                const targetPage = document.getElementById(pageId + '-page');
                if (targetPage) {
                    targetPage.classList.add('active');
                }

                // 更新底部导航状态
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => item.classList.remove('active'));

                // 根据页面ID设置对应的导航项为活跃状态
                const navMap = {
                    'home': 0,
                    'tasks': 1,
                    'wallet': 2,
                    'profile': 3,
                    // 对于其他页面（如referral, vip等），不设置底部导航状态
                    'referral': null,
                    'vip': null,
                    'daily-checkin': null
                };

                const navIndex = navMap[pageId];
                if (navIndex !== undefined && navIndex !== null) {
                    navItems[navIndex].classList.add('active');
                }

                // 更新URL hash
                window.location.hash = pageId;
            }

            // 复制邀请链接
            function copyInviteLink() {
                const inviteCode = 'TK2024ABC';
                const inviteLink = `https://tiktok-platform.com/register?code=${inviteCode}`;
                
                navigator.clipboard.writeText(inviteLink).then(() => {
                    showNotification('邀请链接已复制到剪贴板', 'success');
                }).catch(() => {
                    // 兼容旧浏览器的复制方法
                    const textArea = document.createElement('textarea');
                    textArea.value = inviteLink;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showNotification('邀请链接已复制到剪贴板', 'success');
                });
            }

            // 显示二维码
            function showQRCode() {
                const inviteCode = 'TK2024ABC';
                const inviteLink = `https://tiktok-platform.com/register?code=${inviteCode}`;
                
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
                        <div class="text-center">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">扫码邀请好友</h3>
                            
                            <!-- 二维码区域 -->
                            <div class="bg-gray-100 rounded-2xl p-6 mb-4">
                                <div class="w-48 h-48 mx-auto bg-white rounded-lg flex items-center justify-center border-2 border-gray-200">
                                    <div class="text-center">
                                        <i class="fas fa-qrcode text-6xl text-gray-400 mb-2"></i>
                                        <p class="text-sm text-gray-500">二维码</p>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-600 mt-3">扫描二维码注册</p>
                            </div>
                            
                            <!-- 邀请码显示 -->
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 mb-6">
                                <p class="text-sm text-gray-600 mb-1">邀请码</p>
                                <p class="text-xl font-bold text-green-600">${inviteCode}</p>
                            </div>
                            
                            <!-- 按钮组 -->
                            <div class="flex space-x-3">
                                <button onclick="closeQRModal()" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-all">
                                    关闭
                                </button>
                                <button onclick="copyInviteLink(); closeQRModal()" class="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all">
                                    复制链接
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 点击背景关闭
                modal.onclick = (e) => {
                    if (e.target === modal) {
                        closeQRModal();
                    }
                };
                
                document.body.appendChild(modal);
                
                // 显示动画
                setTimeout(() => {
                    modal.querySelector('.bg-white').classList.remove('scale-95');  
                    modal.querySelector('.bg-white').classList.add('scale-100');
                }, 10);
            }

            // 关闭二维码模态框
            function closeQRModal() {
                const modal = document.querySelector('.fixed.inset-0.bg-black');
                if (modal) {
                    modal.querySelector('.bg-white').classList.add('scale-95');
                    setTimeout(() => {
                        modal.remove();
                    }, 300);
                }
            }

            // 显示通知
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-20 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-full shadow-lg z-50 transition-all duration-300 ${
                    type === 'success' ? 'bg-green-500 text-white' : 
                    type === 'error' ? 'bg-red-500 text-white' : 
                    'bg-gray-800 text-white'
                }`;
                notification.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : 'fa-info'} mr-2"></i>
                        ${message}
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                // 3秒后自动消失
                setTimeout(() => {
                    notification.classList.add('opacity-0', 'transform', 'scale-95');
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }

            // 跳转到任务详情页面
            function goToTaskDetails(taskType, reward, title, description) {
                // 将任务信息存储到localStorage，供任务详情页面使用
                const taskData = {
                    type: taskType,
                    reward: reward,
                    title: title,
                    description: description,
                    timestamp: Date.now()
                };

                localStorage.setItem('currentTask', JSON.stringify(taskData));

                // 保存当前页面信息，供返回时使用
                const currentPage = getCurrentPageFromHash();
                localStorage.setItem('taskSourcePage', currentPage);

                // 跳转到任务详情页面
                window.location.href = 'task-details.html';
            }

            // 获取当前页面标识
            function getCurrentPageFromHash() {
                const hash = window.location.hash;
                if (hash === '#tasks') return 'tasks';
                if (hash === '#home' || hash === '') return 'home';
                if (hash === '#profile') return 'profile';
                return 'home'; // 默认返回首页
            }

            // 自定义确认对话框
            function showConfirm(title, message, onConfirm) {
                const confirmBox = document.getElementById('customConfirm');
                const confirmTitle = document.getElementById('confirmTitle');
                const confirmMessage = document.getElementById('confirmMessage');
                const confirmButton = document.getElementById('confirmButton');
                const cancelButton = document.getElementById('cancelButton');

                confirmTitle.textContent = title;
                confirmMessage.textContent = message;

                // 显示确认框
                confirmBox.classList.remove('hidden');
                setTimeout(() => {
                    confirmBox.querySelector('.bg-white').classList.remove('scale-95');
                    confirmBox.querySelector('.bg-white').classList.add('scale-100');
                }, 10);

                // 绑定事件
                confirmButton.onclick = () => {
                    hideConfirm();
                    onConfirm();
                };

                cancelButton.onclick = () => hideConfirm();
                confirmBox.onclick = (e) => {
                    if (e.target === confirmBox) hideConfirm();
                };
            }

            function hideConfirm() {
                const confirmBox = document.getElementById('customConfirm');
                confirmBox.querySelector('.bg-white').classList.remove('scale-100');
                confirmBox.querySelector('.bg-white').classList.add('scale-95');
                setTimeout(() => {
                    confirmBox.classList.add('hidden');
                }, 300);
            }

            // 退出登录功能
            function logout() {
                showConfirm('退出登录', '确定要退出登录吗？', () => {
                    // 清除本地存储的用户数据
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('userInfo');
                    localStorage.removeItem('currentTask');

                    // 跳转到登录页面
                    window.location.href = 'login.html';
                });
            }

            // 检查登录状态
            function checkLoginStatus() {
                const userToken = localStorage.getItem('userToken');
                if (!userToken) {
                    // 未登录，跳转到登录页面
                    window.location.href = 'login.html';
                    return false;
                }
                return true;
            }

            // 初始化页面
            document.addEventListener('DOMContentLoaded', function () {
                // 检查登录状态
                if (!checkLoginStatus()) {
                    return;
                }

                // 加载用户信息
                loadUserInfo();

                // 检查URL哈希，如果有指定页面则显示对应页面
                const hash = window.location.hash.substring(1);
                if (hash && ['home', 'tasks', 'wallet', 'profile', 'daily-checkin', 'vip', 'referral'].includes(hash)) {
                    showPage(hash);
                } else {
                    showPage('home');
                }
            });

            // 加载用户信息
            function loadUserInfo() {
                const userInfo = localStorage.getItem('userInfo');
                if (userInfo) {
                    const user = JSON.parse(userInfo);
                    // 更新页面中的用户信息显示
                    updateUserDisplay(user);
                }
            }

            // 更新用户信息显示
            function updateUserDisplay(user) {
                // 更新用户昵称
                const nicknameElements = document.querySelectorAll('.user-nickname');
                nicknameElements.forEach(el => {
                    if (el) el.textContent = user.nickname || '用户';
                });

                // 更新余额显示
                const balanceElements = document.querySelectorAll('.user-balance');
                balanceElements.forEach(el => {
                    if (el) el.textContent = (user.balance || 0).toFixed(2);
                });
            }

            // 轮播图功能
            let currentSlide = 0;
            const totalSlides = 3;
            let slideInterval;

            function goToSlide(slideIndex) {
                currentSlide = slideIndex;
                const slider = document.getElementById('bannerSlider');
                const translateX = -slideIndex * 100;
                slider.style.transform = `translateX(${translateX}%)`;

                // 更新指示器
                const dots = document.querySelectorAll('.banner-dot');
                dots.forEach((dot, index) => {
                    if (index === slideIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                goToSlide(currentSlide);
            }

            function startSlideShow() {
                slideInterval = setInterval(nextSlide, 4000); // 每4秒切换
            }

            function stopSlideShow() {
                if (slideInterval) {
                    clearInterval(slideInterval);
                }
            }

            // 页面加载完成后启动轮播
            document.addEventListener('DOMContentLoaded', function () {
                startSlideShow();

                // 鼠标悬停时暂停轮播
                const bannerContainer = document.querySelector('.banner-container');
                if (bannerContainer) {
                    bannerContainer.addEventListener('mouseenter', stopSlideShow);
                    bannerContainer.addEventListener('mouseleave', startSlideShow);
                }
            });

            // 监听哈希变化
            window.addEventListener('hashchange', function () {
                const hash = window.location.hash.substring(1);
                if (hash && ['home', 'tasks', 'wallet', 'profile', 'daily-checkin', 'vip', 'referral'].includes(hash)) {
                    showPage(hash);
                }
            });

            // VIP升级功能 - 从本地存储加载用户数据
            let userData = JSON.parse(localStorage.getItem('userData')) || {
                balance: 128.56,
                vipLevel: 1,
                vipExpireTime: '2024-12-31 23:59:59'
            };

            // VIP等级配置
            const vipConfigs = {
                2: {
                    name: 'VIP 2 - 白银',
                    cost: 30,
                    benefits: ['25个每日任务', '+20%收益加成', '专属任务池']
                },
                3: {
                    name: 'VIP 3 - 黄金',
                    cost: 68,
                    benefits: ['40个每日任务', '+35%收益加成', '1v1专属客服']
                },
                4: {
                    name: 'VIP 4 - 钻石',
                    cost: 128,
                    benefits: ['60个每日任务', '+50%收益加成', '专属高价任务']
                }
            };

            // VIP升级函数
            function upgradeVip(targetLevel, cost) {
                console.log(`[VIP] 升级函数被调用: 目标等级=${targetLevel}, 费用=${cost}`);
                
                const config = vipConfigs[targetLevel];
                if (!config) {
                    alert('VIP配置错误，请刷新页面重试');
                    return;
                }
                
                console.log(`[VIP] 当前VIP等级: ${userData.vipLevel}, 当前余额: ${userData.balance}`);
                
                // 检查是否已经是更高等级
                if (userData.vipLevel >= targetLevel) {
                    showVipNotification('您已经是更高等级的VIP了！', 'warning');
                    return;
                }

                // 检查余额是否足够
                if (userData.balance < cost) {
                    const shortfall = cost - userData.balance;
                    showVipNotification(`余额不足！还需要 ${shortfall.toFixed(2)} USDT，请先充值。`, 'error');
                    return;
                }

                // 显示自定义升级确认对话框
                showVipUpgradeModal(targetLevel, config);
            }

            // 显示VIP升级确认对话框
            function showVipUpgradeModal(targetLevel, config) {
                const modal = document.getElementById('vipUpgradeModal');
                const icon = document.getElementById('vipUpgradeIcon');
                const title = document.getElementById('vipUpgradeTitle');
                const cost = document.getElementById('vipUpgradeCost');
                const currentBalanceEl = document.getElementById('currentBalance');
                const afterBalance = document.getElementById('afterBalance');
                const confirmBtn = document.getElementById('confirmUpgrade');

                // VIP等级颜色配置
                const vipColors = {
                    2: 'bg-gradient-to-r from-purple-500 to-indigo-500',
                    3: 'bg-gradient-to-r from-yellow-400 to-orange-500',
                    4: 'bg-gradient-to-r from-red-500 to-pink-500'
                };

                // 设置图标样式
                icon.className = `w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${vipColors[targetLevel]}`;
                
                // 设置内容
                title.textContent = `升级到 ${config.name}`;
                cost.textContent = `${config.cost} USDT`;
                currentBalanceEl.textContent = `${userData.balance.toFixed(2)} USDT`;
                afterBalance.textContent = `${(userData.balance - config.cost).toFixed(2)} USDT`;

                // 设置特权
                document.getElementById('vipBenefit1').textContent = config.benefits[0];
                document.getElementById('vipBenefit2').textContent = config.benefits[1];
                document.getElementById('vipBenefit3').textContent = config.benefits[2];

                // 设置确认按钮样式
                confirmBtn.className = `flex-1 ${vipColors[targetLevel]} text-white font-semibold py-3 px-6 rounded-xl hover:opacity-90 transition-all shadow-lg`;
                
                // 绑定确认事件
                confirmBtn.onclick = () => confirmVipUpgrade(targetLevel, config);

                // 显示对话框
                modal.classList.remove('hidden');
                setTimeout(() => {
                    modal.querySelector('.bg-white').classList.remove('scale-95');
                    modal.querySelector('.bg-white').classList.add('scale-100');
                }, 10);
            }

            // 确认VIP升级
            function confirmVipUpgrade(targetLevel, config) {
                // 隐藏确认对话框
                hideVipModal('vipUpgradeModal');
                
                // 模拟处理时间
                setTimeout(() => {
                    // 更新用户数据
                    userData.balance -= config.cost;
                    userData.vipLevel = targetLevel;
                    
                    // 更新页面显示
                    updateVipDisplay();
                    
                    // 保存到本地存储
                    localStorage.setItem('userData', JSON.stringify(userData));
                    
                    // 显示成功提示
                    showVipSuccessModal(config.name);
                    
                    console.log(`[VIP] 升级成功！新等级：${targetLevel}，余额：${userData.balance}`);
                }, 300);
            }

            // 显示VIP升级成功对话框
            function showVipSuccessModal(vipName) {
                const modal = document.getElementById('upgradeSuccessModal');
                const text = document.getElementById('upgradeSuccessText');
                
                text.innerHTML = `恭喜您成功升级到 ${vipName}！<br>现在可以享受更多特权和更高收益。`;
                
                modal.classList.remove('hidden');
                setTimeout(() => {
                    modal.querySelector('.bg-white').classList.remove('scale-95');
                    modal.querySelector('.bg-white').classList.add('scale-100');
                }, 10);
            }

            // 隐藏VIP模态框
            function hideVipModal(modalId) {
                const modal = document.getElementById(modalId);
                modal.querySelector('.bg-white').classList.remove('scale-100');
                modal.querySelector('.bg-white').classList.add('scale-95');
                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            }

            // 更新VIP显示
            function updateVipDisplay() {
                const vipNames = ['', 'VIP 1', 'VIP 2', 'VIP 3', 'VIP 4'];
                const vipSubtitles = ['', '青铜会员', '白银会员', '黄金会员', '钻石会员'];
                const vipColors = ['', 'from-purple-500 to-indigo-500', 'from-purple-500 to-indigo-500', 'from-yellow-400 to-orange-500', 'from-red-500 to-pink-500'];
                const vipBgColors = ['', 'bg-gradient-to-r from-purple-500 to-indigo-500', 'bg-gradient-to-r from-purple-500 to-indigo-500', 'bg-gradient-to-r from-yellow-400 to-orange-500', 'bg-gradient-to-r from-red-500 to-pink-500'];
                
                // 更新VIP页面内的显示
                const vipLevelElement = document.querySelector('#vip-page h2');
                const vipSubtitleElement = document.querySelector('#vip-page h2 + p');
                
                if (vipLevelElement) {
                    vipLevelElement.textContent = vipNames[userData.vipLevel];
                }
                if (vipSubtitleElement) {
                    vipSubtitleElement.textContent = vipSubtitles[userData.vipLevel];
                }
                
                // 更新首页用户信息中的VIP等级
                const homeVipBadge = document.querySelector('.vip-badge');
                if (homeVipBadge) {
                    homeVipBadge.textContent = vipNames[userData.vipLevel];
                    // 更新VIP徽章父元素颜色
                    const badgeParent = homeVipBadge.parentElement;
                    if (badgeParent) {
                        badgeParent.className = `px-2 py-1 rounded-full mr-2 ${vipBgColors[userData.vipLevel]}`;
                    }
                }
                
                // 更新个人中心的VIP等级
                const profileVipBadges = document.querySelectorAll('#profile-page .vip-badge');
                profileVipBadges.forEach(badge => {
                    if (badge) {
                        badge.textContent = vipNames[userData.vipLevel];
                        // 更新VIP徽章父元素颜色
                        const badgeParent = badge.parentElement;
                        if (badgeParent) {
                            badgeParent.className = `px-3 py-1 rounded-full flex items-center mr-3 ${vipBgColors[userData.vipLevel]}`;
                        }
                    }
                });
                
                // 更新VIP特权中心的当前等级显示
                const vipCenterLevel = document.querySelector('#vip-page .current-vip-level');
                if (vipCenterLevel) {
                    vipCenterLevel.textContent = vipNames[userData.vipLevel];
                }
                
                const vipCenterSubtitle = document.querySelector('#vip-page .current-vip-subtitle');
                if (vipCenterSubtitle) {
                    vipCenterSubtitle.textContent = vipSubtitles[userData.vipLevel];
                }
                
                // 更新VIP特权显示
                updateVipPrivileges();
                
                // 更新VIP等级对比表
                updateVipComparisonTable();
                
                // 更新任务大厅显示
                updateTaskHall();
                
                // 更新余额显示
                updateBalanceDisplay();
                
                // 更新按钮状态
                updateVipButtons();
                
                console.log(`[VIP] 更新显示完成，当前等级：${userData.vipLevel}`);
            }

            // 更新任务大厅显示
            function updateTaskHall() {
                const vipTasksConfig = {
                    1: { daily: 15, available: 156, specialTasks: [] },
                    2: { daily: 25, available: 235, specialTasks: ['评论任务'] },
                    3: { daily: 40, available: 380, specialTasks: ['评论任务', '分享任务'] },
                    4: { daily: 60, available: 560, specialTasks: ['评论任务', '分享任务', '直播任务'] }
                };

                const currentConfig = vipTasksConfig[userData.vipLevel] || vipTasksConfig[1];
                
                // 更新首页快捷入口的任务数
                const homeTaskCount = document.querySelector('.available-tasks-count');
                if (homeTaskCount) {
                    homeTaskCount.textContent = `${currentConfig.available}个任务`;
                }
                
                // 更新任务大厅页面的可用任务数
                const taskHallNumber = document.querySelector('.available-tasks-number');
                if (taskHallNumber) {
                    taskHallNumber.textContent = currentConfig.available.toString();
                }
                
                // 更新任务列表
                updateTaskList(currentConfig);
                
                console.log(`[Task] 任务大厅更新完成，VIP${userData.vipLevel}可用任务：${currentConfig.available}个`);
            }

            // 更新任务列表
            function updateTaskList(taskConfig) {
                const taskListContainer = document.getElementById('task-list');
                if (!taskListContainer) return;

                const baseTasks = [
                    {
                        id: 'like',
                        name: 'TikTok点赞任务',
                        desc: '为指定视频点赞',
                        price: '$2.50',
                        icon: 'fas fa-heart',
                        color: 'from-red-500 to-pink-500',
                        textColor: 'text-red-600',
                        btnColor: 'bg-red-500',
                        minVip: 1
                    },
                    {
                        id: 'follow',
                        name: 'TikTok关注任务',
                        desc: '关注指定用户账号',
                        price: '$5.00',
                        icon: 'fas fa-user-plus',
                        color: 'from-blue-500 to-indigo-500',
                        textColor: 'text-blue-600',
                        btnColor: 'bg-blue-500',
                        minVip: 1
                    },
                    {
                        id: 'comment',
                        name: 'TikTok评论任务',
                        desc: '发表正面评论',
                        price: '$8.00',
                        icon: 'fas fa-comment',
                        color: 'from-yellow-500 to-orange-500',
                        textColor: 'text-yellow-600',
                        btnColor: 'bg-yellow-500',
                        minVip: 2,
                        vipTag: 'VIP2+'
                    },
                    {
                        id: 'share',
                        name: 'TikTok分享任务',
                        desc: '分享视频到朋友圈',
                        price: '$12.00',
                        icon: 'fas fa-share',
                        color: 'from-green-500 to-emerald-500',
                        textColor: 'text-green-600',
                        btnColor: 'bg-green-500',
                        minVip: 3,
                        vipTag: 'VIP3+'
                    },
                    {
                        id: 'live',
                        name: 'TikTok直播任务',
                        desc: '观看指定直播5分钟',
                        price: '$20.00',
                        icon: 'fas fa-video',
                        color: 'from-purple-500 to-indigo-500',
                        textColor: 'text-purple-600',
                        btnColor: 'bg-purple-500',
                        minVip: 4,
                        vipTag: 'VIP4专享'
                    }
                ];

                // 生成任务HTML
                const taskHTML = baseTasks.map(task => {
                    const canAccess = userData.vipLevel >= task.minVip;
                    const vipTagHTML = task.vipTag ? `<span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full ml-2">${task.vipTag}</span>` : '';
                    
                    return `
                        <div class="bg-white rounded-2xl shadow-lg p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gradient-to-r ${task.color} rounded-full flex items-center justify-center mr-4">
                                        <i class="${task.icon} text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">${task.name}</h3>
                                        <div class="flex items-center">
                                            <p class="text-sm text-gray-600">${task.desc}</p>
                                            ${vipTagHTML}
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xl font-bold ${task.textColor}">${task.price}</p>
                                    <button class="mt-2 ${canAccess ? task.btnColor : 'bg-gray-400'} text-white px-4 py-1 rounded-full text-sm ${canAccess ? '' : 'cursor-not-allowed'}"
                                            ${canAccess ? `onclick="goToTaskDetails('${task.id}', '${task.price.replace('$', '')}', '${task.name}', '${task.desc}')"` : 'onclick="showPage(\'vip\')" disabled'}>
                                        ${canAccess ? '开始' : '升级VIP'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                taskListContainer.innerHTML = taskHTML;
            }

            // 更新VIP等级对比表
            function updateVipComparisonTable() {
                // 重置所有VIP卡片样式
                const allCards = document.querySelectorAll('.vip-comparison-card');
                allCards.forEach(card => {
                    const level = parseInt(card.dataset.vipLevel);
                    const statusBadge = card.querySelector('.vip-status-badge');
                    
                    if (level === userData.vipLevel) {
                        // 当前等级：蓝色高亮
                        card.className = 'vip-comparison-card border-2 border-indigo-500 bg-indigo-50 rounded-xl p-4';
                        if (card.dataset.vipLevel === '4') {
                            card.className += ' relative';
                        }
                        if (statusBadge) {
                            statusBadge.textContent = '当前';
                            statusBadge.className = 'vip-status-badge bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium';
                        }
                    } else if (level < userData.vipLevel) {
                        // 已拥有等级：绿色
                        card.className = 'vip-comparison-card border-2 border-green-500 bg-green-50 rounded-xl p-4';
                        if (card.dataset.vipLevel === '4') {
                            card.className += ' relative';
                        }
                        if (statusBadge) {
                            statusBadge.textContent = '已拥有';
                            statusBadge.className = 'vip-status-badge bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium';
                        }
                    } else {
                        // 未拥有等级：灰色边框，显示升级按钮
                        card.className = 'vip-comparison-card border border-gray-200 rounded-xl p-4';
                        if (card.dataset.vipLevel === '4') {
                            card.className += ' relative';
                        }
                        if (statusBadge) {
                            statusBadge.textContent = '升级';
                            // 保持原有的升级按钮样式
                            if (level === 2) {
                                statusBadge.className = 'vip-status-badge bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-purple-600 transition-colors';
                            } else if (level === 3) {
                                statusBadge.className = 'vip-status-badge bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-yellow-500 hover:to-yellow-700 transition-all';
                            } else if (level === 4) {
                                statusBadge.className = 'vip-status-badge bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg hover:from-red-600 hover:to-pink-600 transition-all';
                            }
                        }
                    }
                });
                
                console.log(`[VIP] 等级对比表更新完成，当前等级：VIP${userData.vipLevel}`);
            }

            // 更新VIP特权显示
            function updateVipPrivileges() {
                const vipPrivileges = {
                    1: {
                        dailyTasks: '15个/天',
                        incomeBonus: '+10%',
                        customerService: '优先处理',
                        birthdayGift: '专属奖励'
                    },
                    2: {
                        dailyTasks: '25个/天',
                        incomeBonus: '+20%',
                        customerService: '专属客服',
                        birthdayGift: '精美礼品'
                    },
                    3: {
                        dailyTasks: '40个/天',
                        incomeBonus: '+35%',
                        customerService: '1v1专属客服',
                        birthdayGift: '豪华礼包'
                    },
                    4: {
                        dailyTasks: '60个/天',
                        incomeBonus: '+50%',
                        customerService: '24h专属客服',
                        birthdayGift: '尊享大礼包'
                    }
                };

                const currentPrivileges = vipPrivileges[userData.vipLevel] || vipPrivileges[1];
                
                // 更新特权显示
                const dailyTasksElement = document.querySelector('.daily-tasks');
                if (dailyTasksElement) {
                    dailyTasksElement.textContent = currentPrivileges.dailyTasks;
                }
                
                const incomeBonusElement = document.querySelector('.income-bonus');
                if (incomeBonusElement) {
                    incomeBonusElement.textContent = currentPrivileges.incomeBonus;
                }
                
                const customerServiceElement = document.querySelector('.customer-service');
                if (customerServiceElement) {
                    customerServiceElement.textContent = currentPrivileges.customerService;
                }
                
                const birthdayGiftElement = document.querySelector('.birthday-gift');
                if (birthdayGiftElement) {
                    birthdayGiftElement.textContent = currentPrivileges.birthdayGift;
                }
                
                console.log(`[VIP] 特权更新完成，当前等级：VIP${userData.vipLevel}`);
            }

            // 更新余额显示
            function updateBalanceDisplay() {
                // 更新首页余额显示
                const homeBalance = document.querySelector('.user-info .balance-amount');
                if (homeBalance) {
                    homeBalance.textContent = userData.balance.toFixed(2);
                }
                
                // 更新钱包页面余额显示
                const walletBalance = document.querySelector('#wallet-page .balance-amount');
                if (walletBalance) {
                    walletBalance.textContent = userData.balance.toFixed(2);
                }
                
                // 更新个人中心余额显示 (如果存在)
                const profileBalance = document.querySelector('#profile-page .balance-amount');
                if (profileBalance) {
                    profileBalance.textContent = userData.balance.toFixed(2);
                }
                
                console.log(`[VIP] 余额更新为：${userData.balance.toFixed(2)} USDT`);
            }

            // 更新VIP按钮状态
            function updateVipButtons() {
                const buttons = document.querySelectorAll('#vip-page button[onclick*="upgradeVip"]');
                buttons.forEach((button, index) => {
                    const level = index + 2; // VIP2, VIP3, VIP4
                    if (level <= userData.vipLevel) {
                        button.textContent = '已拥有';
                        button.className = 'bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium cursor-default';
                        button.onclick = null;
                    }
                });
            }

            // 显示VIP通知
            function showVipNotification(message, type = 'info') {
                const bgColor = type === 'error' ? 'bg-red-500' : 
                               type === 'warning' ? 'bg-yellow-500' : 
                               type === 'success' ? 'bg-green-500' : 'bg-blue-500';
                
                const notification = document.createElement('div');
                notification.className = `fixed top-4 left-1/2 transform -translate-x-1/2 ${bgColor} text-white px-6 py-3 rounded-xl shadow-lg z-50 transition-all duration-300`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => notification.remove(), 3000);
            }

            // 从本地存储加载用户数据
            const savedUserData = localStorage.getItem('userData');
            if (savedUserData) {
                userData = JSON.parse(savedUserData);
                console.log('[VIP] 从本地存储加载用户数据:', userData);
            }

            // 测试函数 - 可以在控制台调用
            window.testVipUpgrade = function() {
                console.log('[VIP] 测试升级功能');
                upgradeVip(2, 30);
            };

            // 绑定对话框事件
            document.addEventListener('DOMContentLoaded', function() {
                // 取消升级按钮
                const cancelBtn = document.getElementById('cancelUpgrade');
                if (cancelBtn) {
                    cancelBtn.onclick = () => hideVipModal('vipUpgradeModal');
                }
                
                // 关闭成功提示
                const closeBtn = document.getElementById('closeSuccessModal');
                if (closeBtn) {
                    closeBtn.onclick = () => hideVipModal('upgradeSuccessModal');
                }
                
                // 点击模态框背景关闭
                const upgradeModal = document.getElementById('vipUpgradeModal');
                if (upgradeModal) {
                    upgradeModal.onclick = (e) => {
                        if (e.target.id === 'vipUpgradeModal') {
                            hideVipModal('vipUpgradeModal');
                        }
                    };
                }
                
                const successModal = document.getElementById('upgradeSuccessModal');
                if (successModal) {
                    successModal.onclick = (e) => {
                        if (e.target.id === 'upgradeSuccessModal') {
                            hideVipModal('upgradeSuccessModal');
                        }
                    };
                }
                
                // 禁用双击缩放
                let lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    const now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);
                
                // 禁用双击事件
                document.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    return false;
                }, { passive: false });
                
                // 初始化时更新所有VIP和余额显示
                setTimeout(() => {
                    updateVipDisplay();
                    console.log(`[VIP] 页面加载完成，初始化用户数据：VIP${userData.vipLevel}, 余额：${userData.balance}`);
                }, 100);
            });

        </script>
    </div> <!-- 关闭移动端容器 -->

    <!-- VIP升级确认对话框 -->
    <div id="vipUpgradeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden transition-all duration-300">
        <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <!-- VIP图标 -->
                <div id="vipUpgradeIcon" class="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center">
                    <i class="fas fa-crown text-4xl text-white"></i>
                </div>
                
                <!-- 标题 -->
                <h3 id="vipUpgradeTitle" class="text-xl font-bold text-gray-900 mb-3">升级到 VIP 2</h3>
                
                <!-- 费用信息 -->
                <div class="bg-gray-50 rounded-2xl p-4 mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">升级费用</span>
                        <span id="vipUpgradeCost" class="font-bold text-lg text-red-600">30 USDT</span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">当前余额</span>
                        <span id="currentBalance" class="font-bold text-lg text-indigo-600">128.56 USDT</span>
                    </div>
                    <hr class="my-2 border-gray-200">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">升级后余额</span>
                        <span id="afterBalance" class="font-bold text-lg text-green-600">98.56 USDT</span>
                    </div>
                </div>
                
                <!-- 特权列表 -->
                <div class="bg-blue-50 rounded-2xl p-4 mb-6">
                    <h4 class="font-semibold text-blue-900 mb-3 flex items-center justify-center">
                        <i class="fas fa-star text-yellow-500 mr-2"></i>
                        升级后将享受
                    </h4>
                    <div class="space-y-2 text-left">
                        <div class="flex items-center text-sm text-blue-800">
                            <i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i>
                            <span id="vipBenefit1">25个每日任务</span>
                        </div>
                        <div class="flex items-center text-sm text-blue-800">
                            <i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i>
                            <span id="vipBenefit2">+20%收益加成</span>
                        </div>
                        <div class="flex items-center text-sm text-blue-800">
                            <i class="fas fa-check text-green-500 mr-3 flex-shrink-0"></i>
                            <span id="vipBenefit3">专属任务池</span>
                        </div>
                    </div>
                </div>
                
                <!-- 按钮组 -->
                <div class="flex space-x-3">
                    <button id="cancelUpgrade" class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                        取消
                    </button>
                    <button id="confirmUpgrade" class="flex-1 text-white font-semibold py-3 px-6 rounded-xl transition-all">
                        确认升级
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- VIP升级成功提示 -->
    <div id="upgradeSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden transition-all duration-300">
        <div class="bg-white rounded-3xl p-8 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <!-- 成功图标 -->
                <div class="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                    <i class="fas fa-check text-white text-3xl"></i>
                </div>
                
                <!-- 成功标题 -->
                <h3 class="text-xl font-bold text-gray-900 mb-3">🎉 升级成功！</h3>
                
                <!-- 成功信息 -->
                <div class="bg-green-50 rounded-2xl p-4 mb-6">
                    <p id="upgradeSuccessText" class="text-green-800 font-medium">
                        恭喜您成功升级到 VIP 2 - 白银！<br>
                        现在可以享受更多特权和更高收益。
                    </p>
                </div>
                
                <!-- 关闭按钮 -->
                <button id="closeSuccessModal" class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg">
                    <i class="fas fa-rocket mr-2"></i>
                    开始享受特权
                </button>
            </div>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="customConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div
            class="bg-white rounded-2xl p-6 mx-6 max-w-sm w-full shadow-2xl transform transition-all duration-300 scale-95">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center bg-red-100">
                    <i class="fas fa-sign-out-alt text-2xl text-red-600"></i>
                </div>
                <h3 id="confirmTitle" class="text-lg font-semibold text-gray-900 mb-2"></h3>
                <p id="confirmMessage" class="text-gray-600 mb-6"></p>
                <div class="flex space-x-3">
                    <button id="cancelButton"
                        class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 transition-all">
                        取消
                    </button>
                    <button id="confirmButton"
                        class="flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold py-3 px-6 rounded-xl hover:from-red-600 hover:to-pink-600 transition-all">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 处理URL hash变化
        function handleHashChange() {
            const hash = window.location.hash.substring(1); // 移除 # 符号
            const validPages = ['home', 'tasks', 'wallet', 'profile', 'daily-checkin', 'vip', 'referral'];

            if (validPages.includes(hash)) {
                showPage(hash);
            } else {
                // 默认显示首页
                showPage('home');
            }
        }

        // 个人中心页面导航函数
        function openEditProfile() {
            window.location.href = 'edit-profile.html';
        }

        function openSecuritySettings() {
            window.location.href = 'security-settings.html';
        }

        function openChangePassword() {
            window.location.href = 'change-password.html';
        }

        function openNotifications() {
            window.location.href = 'notifications.html';
        }

        // 监听hash变化
        window.addEventListener('hashchange', handleHashChange);

        // 页面加载时处理初始hash
        window.addEventListener('load', handleHashChange);
    </script>
</body>

</html>