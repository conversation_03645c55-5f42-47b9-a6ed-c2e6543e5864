<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址簿 - TikTok任务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f3f4f6;
        }
        
        .mobile-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 弹窗动画效果 */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        .modal-exit {
            animation: modalExit 0.3s ease-in;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalExit {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
        }

        .modal-backdrop {
            backdrop-filter: blur(4px);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .mobile-container { width: 100%; }
        }
        
        @media (max-width: 480px) {
            .px-6 { padding-left: 1rem; padding-right: 1rem; }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg text-white p-6 pb-8">
            <div class="flex items-center justify-between">
                <button onclick="goBack()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </button>
                <h1 class="text-xl font-bold">地址簿</h1>
                <button onclick="showAddAddressModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-plus text-xl"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="px-6 py-6 -mt-4">
            <!-- 地址列表 -->
            <div class="bg-white rounded-2xl shadow-lg fade-in" id="addressList">
                <!-- 地址项将通过JavaScript动态生成 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-12 hidden">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-address-book text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无保存的地址</h3>
                <p class="text-gray-500 text-sm mb-6">添加常用的提现地址，下次提现更便捷</p>
                <button onclick="showAddAddressModal()" 
                    class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    <i class="fas fa-plus mr-2"></i>添加地址
                </button>
            </div>
        </div>
    </div>

    <!-- 添加/编辑地址弹窗 -->
    <div id="addressModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-md w-full shadow-2xl transform transition-all modal-enter">
            <!-- 弹窗头部 -->
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900" id="modalTitle">添加地址</h3>
                <button onclick="closeAddressModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- 表单 -->
            <form id="addressForm">
                <!-- 地址标签 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">地址标签</label>
                    <input type="text" id="addressLabel" placeholder="例如：我的钱包、交易所地址" 
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                </div>

                <!-- 网络类型 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">网络类型</label>
                    <div class="grid grid-cols-2 gap-3">
                        <button type="button" onclick="selectModalNetwork('trc20')" 
                            class="network-option border-2 border-gray-200 rounded-xl p-3 text-center hover:border-indigo-500 transition-colors" 
                            data-network="trc20">
                            <div class="text-sm font-semibold text-gray-900">TRC20</div>
                            <div class="text-xs text-gray-500">手续费: 2 USDT</div>
                        </button>
                        <button type="button" onclick="selectModalNetwork('erc20')" 
                            class="network-option border-2 border-gray-200 rounded-xl p-3 text-center hover:border-indigo-500 transition-colors" 
                            data-network="erc20">
                            <div class="text-sm font-semibold text-gray-900">ERC20</div>
                            <div class="text-xs text-gray-500">手续费: 15 USDT</div>
                        </button>
                    </div>
                </div>

                <!-- 钱包地址 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">钱包地址</label>
                    <div class="relative">
                        <input type="text" id="modalAddress" placeholder="请输入或粘贴USDT地址" 
                            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all">
                        <button type="button" onclick="pasteModalAddress()" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-indigo-600 transition-colors">
                            <i class="fas fa-paste"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">请确保地址正确，转账无法撤销</p>
                </div>

                <!-- 按钮组 -->
                <div class="flex space-x-3">
                    <button type="button" onclick="closeAddressModal()" 
                        class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-colors">
                        取消
                    </button>
                    <button type="submit" id="saveAddressBtn"
                        class="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 提示弹窗 -->
    <div id="alertModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <div class="text-center">
                <div id="alertIcon" class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h3 id="alertTitle" class="text-xl font-bold text-gray-900 mb-2">提示</h3>
                <p id="alertMessage" class="text-gray-600 text-sm mb-6">请填写完整信息</p>

                <button onclick="closeAlertModal()"
                    class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all shadow-lg">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl transform transition-all modal-enter">
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-trash text-red-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">删除地址</h3>
                <p class="text-gray-600 text-sm mb-6">确定要删除这个地址吗？此操作无法撤销。</p>
                
                <div class="flex space-x-3">
                    <button onclick="closeDeleteModal()" 
                        class="flex-1 bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-xl hover:bg-gray-200 transition-colors">
                        取消
                    </button>
                    <button onclick="confirmDelete()" 
                        class="flex-1 bg-red-500 text-white font-semibold py-3 px-4 rounded-xl hover:bg-red-600 transition-all shadow-lg">
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let addresses = [];
        let editingIndex = -1;
        let deletingIndex = -1;
        let selectedNetwork = 'trc20';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAddresses();
            renderAddresses();
        });

        // 加载地址数据
        function loadAddresses() {
            const saved = localStorage.getItem('savedAddresses');
            if (saved) {
                addresses = JSON.parse(saved);
            }
        }

        // 保存地址数据
        function saveAddresses() {
            localStorage.setItem('savedAddresses', JSON.stringify(addresses));
        }

        // 渲染地址列表
        function renderAddresses() {
            const addressList = document.getElementById('addressList');
            const emptyState = document.getElementById('emptyState');

            if (addresses.length === 0) {
                addressList.classList.add('hidden');
                emptyState.classList.remove('hidden');
                return;
            }

            addressList.classList.remove('hidden');
            emptyState.classList.add('hidden');

            addressList.innerHTML = addresses.map((addr, index) => `
                <div class="border-b border-gray-100 last:border-b-0">
                    <div class="p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <div class="w-10 h-10 ${addr.network === 'trc20' ? 'bg-green-100' : 'bg-blue-100'} rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-wallet ${addr.network === 'trc20' ? 'text-green-600' : 'text-blue-600'} text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">${addr.label}</h4>
                                        <span class="text-xs px-2 py-1 ${addr.network === 'trc20' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'} rounded-full">
                                            ${addr.network.toUpperCase()}
                                        </span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-500 font-mono break-all">${addr.address}</p>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <button onclick="editAddress(${index})"
                                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-indigo-100 hover:text-indigo-600 transition-colors">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                <button onclick="deleteAddress(${index})"
                                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-red-100 hover:text-red-600 transition-colors">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                                <button onclick="selectAddress(${index})"
                                    class="px-3 py-1 bg-indigo-500 text-white text-xs rounded-full hover:bg-indigo-600 transition-colors">
                                    选择
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 显示添加地址弹窗
        function showAddAddressModal() {
            editingIndex = -1;
            document.getElementById('modalTitle').textContent = '添加地址';
            document.getElementById('addressLabel').value = '';
            document.getElementById('modalAddress').value = '';
            selectModalNetwork('trc20');

            const modal = document.getElementById('addressModal');
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 编辑地址
        function editAddress(index) {
            editingIndex = index;
            const addr = addresses[index];

            document.getElementById('modalTitle').textContent = '编辑地址';
            document.getElementById('addressLabel').value = addr.label;
            document.getElementById('modalAddress').value = addr.address;
            selectModalNetwork(addr.network);

            const modal = document.getElementById('addressModal');
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 删除地址
        function deleteAddress(index) {
            deletingIndex = index;
            const modal = document.getElementById('deleteModal');
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 选择地址（用于提现）
        function selectAddress(index) {
            const addr = addresses[index];
            // 将选中的地址信息传回提现页面
            localStorage.setItem('selectedAddress', JSON.stringify(addr));
            // 返回提现页面
            window.location.href = 'withdraw.html';
        }

        // 选择网络类型
        function selectModalNetwork(network) {
            selectedNetwork = network;
            document.querySelectorAll('.network-option').forEach(btn => {
                btn.classList.remove('border-indigo-500', 'bg-indigo-50');
                btn.classList.add('border-gray-200');
            });

            const selectedBtn = document.querySelector(`[data-network="${network}"]`);
            selectedBtn.classList.remove('border-gray-200');
            selectedBtn.classList.add('border-indigo-500', 'bg-indigo-50');
        }

        // 粘贴地址
        function pasteModalAddress() {
            navigator.clipboard.readText().then(text => {
                document.getElementById('modalAddress').value = text;
            });
        }

        // 关闭地址弹窗
        function closeAddressModal() {
            const modal = document.getElementById('addressModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 显示提示弹窗
        function showAlert(title, message, type = 'error') {
            const modal = document.getElementById('alertModal');
            const icon = document.getElementById('alertIcon');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');

            // 设置内容
            titleEl.textContent = title;
            messageEl.textContent = message;

            // 设置图标和颜色
            if (type === 'error') {
                icon.className = 'w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>';
            } else if (type === 'success') {
                icon.className = 'w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-check-circle text-green-600 text-2xl"></i>';
            } else if (type === 'warning') {
                icon.className = 'w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4';
                icon.innerHTML = '<i class="fas fa-exclamation-circle text-yellow-600 text-2xl"></i>';
            }

            // 显示弹窗
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.add('modal-enter');
            }, 10);
        }

        // 关闭提示弹窗
        function closeAlertModal() {
            const modal = document.getElementById('alertModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 关闭删除弹窗
        function closeDeleteModal() {
            const modal = document.getElementById('deleteModal');
            const modalContent = modal.querySelector('.bg-white');

            modalContent.classList.remove('modal-enter');
            modalContent.classList.add('modal-exit');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('modal-exit');
            }, 300);
        }

        // 确认删除
        function confirmDelete() {
            if (deletingIndex >= 0) {
                addresses.splice(deletingIndex, 1);
                saveAddresses();
                renderAddresses();
                deletingIndex = -1;
            }
            closeDeleteModal();
        }

        // 表单提交
        document.getElementById('addressForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const label = document.getElementById('addressLabel').value.trim();
            const address = document.getElementById('modalAddress').value.trim();

            // 验证表单
            if (!label) {
                showAlert('信息不完整', '请输入地址标签，方便您识别这个地址', 'warning');
                return;
            }

            if (!address) {
                showAlert('信息不完整', '请输入钱包地址', 'warning');
                return;
            }

            if (address.length < 20) {
                showAlert('地址格式错误', '请输入有效的钱包地址，地址长度应不少于20位', 'error');
                return;
            }

            // 检查地址是否已存在
            const existingIndex = addresses.findIndex(addr =>
                addr.address.toLowerCase() === address.toLowerCase() &&
                addr.network === selectedNetwork
            );

            if (existingIndex >= 0 && existingIndex !== editingIndex) {
                showAlert('地址已存在', '该地址已经在地址簿中，请检查后重新输入', 'warning');
                return;
            }

            const addressData = {
                label: label,
                address: address,
                network: selectedNetwork,
                createdAt: new Date().toISOString()
            };

            if (editingIndex >= 0) {
                addresses[editingIndex] = addressData;
                showAlert('保存成功', '地址信息已更新', 'success');
            } else {
                addresses.push(addressData);
                showAlert('添加成功', '新地址已保存到地址簿', 'success');
            }

            saveAddresses();
            renderAddresses();
            closeAddressModal();

            // 成功提示2秒后自动关闭
            setTimeout(() => {
                closeAlertModal();
            }, 2000);
        });

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const addressModal = document.getElementById('addressModal');
            const deleteModal = document.getElementById('deleteModal');
            const alertModal = document.getElementById('alertModal');

            if (e.target === addressModal) {
                closeAddressModal();
            }
            if (e.target === deleteModal) {
                closeDeleteModal();
            }
            if (e.target === alertModal) {
                closeAlertModal();
            }
        });
    </script>
</body>
</html>
